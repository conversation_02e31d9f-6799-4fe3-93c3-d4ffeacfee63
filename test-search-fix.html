<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本搜索功能修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background-color: #f0f4ff;
            border-radius: 20px;
            padding: 8px 16px;
            margin-bottom: 16px;
            border: none;
            transition: all 0.2s ease;
            gap: 8px;
        }
        
        .search-bar:focus-within {
            background-color: #e8f0ff;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
        }
        
        .search-icon {
            margin-right: 8px;
            color: #1976d2;
            font-size: 14px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 14px;
            outline: none;
            color: #333;
        }
        
        .search-input::placeholder {
            color: #999;
            font-size: 14px;
        }
        
        .search-btn {
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 4px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .search-btn:hover {
            background: #1565c0;
            transform: translateY(-1px);
        }
        
        .search-btn:active {
            transform: translateY(0);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
        }
        
        .test-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 8px;
            color: #1565c0;
        }
        
        .test-steps {
            margin-top: 15px;
        }
        
        .test-steps ol {
            margin-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>搜索功能修复测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <div class="test-steps">
                <ol>
                    <li>在下面的搜索框中输入多个字符</li>
                    <li>验证输入框不会在输入过程中失去焦点</li>
                    <li>可以连续输入文字</li>
                    <li>鼠标离开搜索框区域时触发搜索</li>
                    <li>点击"查找"按钮也会触发搜索</li>
                </ol>
            </div>
        </div>
        
        <div class="search-bar" id="searchBar">
            <span class="search-icon">🔍</span>
            <input 
                type="text" 
                placeholder="搜索笔记或待办..." 
                class="search-input"
                id="searchInput"
            />
            <button class="search-btn" id="searchBtn">查找</button>
        </div>
        
        <div class="result" id="result" style="display: none;">
            <h4>搜索结果：</h4>
            <p id="searchText"></p>
            <p><small>搜索时间: <span id="searchTime"></span></small></p>
        </div>
    </div>
    
    <script>
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const result = document.getElementById('result');
        const searchText = document.getElementById('searchText');
        const searchTime = document.getElementById('searchTime');
        
        // 执行搜索
        function performSearch() {
            const query = searchInput.value.trim();
            if (query) {
                searchText.textContent = `"${query}"`;
                searchTime.textContent = new Date().toLocaleTimeString();
                result.style.display = 'block';
                console.log('搜索执行:', query);
            } else {
                result.style.display = 'none';
            }
        }
        
        // 处理鼠标离开搜索框区域
        searchBar.addEventListener('mouseleave', () => {
            if (searchInput.value.trim()) {
                performSearch();
            }
        });
        
        // 处理回车键
        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                performSearch();
            }
        });
        
        // 处理搜索按钮点击
        searchBtn.addEventListener('click', performSearch);
        
        // 监听输入变化（仅用于调试，不触发搜索）
        searchInput.addEventListener('input', (event) => {
            console.log('输入变化:', event.target.value);
            console.log('输入框是否有焦点:', document.activeElement === searchInput);
        });
        
        // 监听焦点变化
        searchInput.addEventListener('focus', () => {
            console.log('输入框获得焦点');
        });
        
        searchInput.addEventListener('blur', () => {
            console.log('输入框失去焦点');
        });
    </script>
</body>
</html>
