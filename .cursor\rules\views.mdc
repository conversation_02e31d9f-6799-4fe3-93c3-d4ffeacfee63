---
description: 
globs: 
alwaysApply: false
---
# Views Directory Guide

The [src/views/](mdc:src/views) directory contains the main view components for the application. These are typically mapped to routes and represent major sections of the app:

- [Notes.vue](mdc:src/views/Notes.vue): The main view for managing and displaying notes.
- [Todos.vue](mdc:src/views/Todos.vue): The main view for managing and displaying todo lists.
- [Main.vue](mdc:src/views/Main.vue): The application's main or landing view.

Views often compose multiple components from [src/components/](mdc:src/components) and handle higher-level logic and layout.

