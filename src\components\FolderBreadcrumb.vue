<template>
  <div v-if="currentFolderId" class="folder-breadcrumb">
    <!-- 根目录链接 -->
    <button 
      class="breadcrumb-item root"
      @click="navigateToFolder(null)"
    >
      <span class="breadcrumb-icon">🏠</span>
      <span class="breadcrumb-text">所有笔记</span>
    </button>
    
    <!-- 路径分隔符和文件夹路径 -->
    <template v-for="(folder, index) in folderPath" :key="folder.id">
      <span class="breadcrumb-separator">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"/>
        </svg>
      </span>
      
      <button 
        v-if="index < folderPath.length - 1"
        class="breadcrumb-item"
        @click="navigateToFolder(folder.id)"
      >
        <span class="breadcrumb-icon">📁</span>
        <span class="breadcrumb-text">{{ folder.name }}</span>
      </button>
      
      <!-- 当前文件夹（不可点击） -->
      <span 
        v-else
        class="breadcrumb-item current"
      >
        <span class="breadcrumb-icon">📁</span>
        <span class="breadcrumb-text">{{ folder.name }}</span>
      </span>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import type { Folder } from '../types/folder.js';
import { FolderService } from '../services/FolderService.js';

// Props
interface Props {
  currentFolderId?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  currentFolderId: null
});

// Emits
const emit = defineEmits<{
  'navigate': [folderId: string | null];
}>();

// 响应式数据
const folderPath = ref<Folder[]>([]);

// 计算属性
const currentFolderName = computed(() => {
  if (!props.currentFolderId || folderPath.value.length === 0) {
    return '';
  }
  return folderPath.value[folderPath.value.length - 1]?.name || '';
});

// 方法
const loadFolderPath = () => {
  if (!props.currentFolderId) {
    folderPath.value = [];
    return;
  }
  
  try {
    folderPath.value = FolderService.getFolderPath(props.currentFolderId);
  } catch (error) {
    console.error('加载文件夹路径失败:', error);
    folderPath.value = [];
  }
};

const navigateToFolder = (folderId: string | null) => {
  emit('navigate', folderId);
};

// 监听当前文件夹变化
watch(() => props.currentFolderId, () => {
  loadFolderPath();
}, { immediate: true });
</script>

<style scoped>
.folder-breadcrumb {
  display: flex;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  text-decoration: none;
}

.breadcrumb-item:hover {
  background-color: #e3f2fd;
  color: #1976d2;
}

.breadcrumb-item.root {
  color: #1976d2;
  font-weight: 500;
}

.breadcrumb-item.root:hover {
  background-color: #e3f2fd;
}

.breadcrumb-item.current {
  color: #333;
  font-weight: 500;
  cursor: default;
  background-color: #fff;
  border: 1px solid #e0e0e0;
}

.breadcrumb-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.breadcrumb-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.breadcrumb-separator {
  display: flex;
  align-items: center;
  margin: 0 4px;
  color: #ccc;
  flex-shrink: 0;
}

.breadcrumb-separator svg {
  width: 12px;
  height: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .folder-breadcrumb {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .breadcrumb-text {
    max-width: 80px;
  }
  
  .breadcrumb-item {
    padding: 3px 6px;
  }
}

/* 动画效果 */
.folder-breadcrumb {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>