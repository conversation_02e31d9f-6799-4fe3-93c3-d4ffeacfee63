// 文件夹工具函数

import type { Folder, FolderTreeNode, NoteWithFolder, FolderStats } from '../types/folder';

/**
 * 构建文件夹树结构
 */
export function buildFolderTree(folders: Folder[], notes: NoteWithFolder[]): FolderTreeNode[] {
  const folderMap = new Map<string, FolderTreeNode>();
  const rootNodes: FolderTreeNode[] = [];
  
  // 创建所有节点
  folders.forEach(folder => {
    folderMap.set(folder.id, {
      folder,
      children: [],
      noteCount: 0
    });
  });
  
  // 计算每个文件夹的笔记数量
  notes.forEach(note => {
    if (note.folderId && folderMap.has(note.folderId)) {
      const node = folderMap.get(note.folderId)!;
      node.noteCount++;
    }
  });
  
  // 构建树结构
  folders.forEach(folder => {
    const node = folderMap.get(folder.id)!;
    
    if (folder.parentId && folderMap.has(folder.parentId)) {
      // 添加到父节点
      const parentNode = folderMap.get(folder.parentId)!;
      parentNode.children.push(node);
    } else {
      // 根节点
      rootNodes.push(node);
    }
  });
  
  // 按名称排序
  const sortNodes = (nodes: FolderTreeNode[]) => {
    nodes.sort((a, b) => a.folder.name.localeCompare(b.folder.name));
    nodes.forEach(node => sortNodes(node.children));
  };
  
  sortNodes(rootNodes);
  return rootNodes;
}

/**
 * 获取文件夹的所有子文件夹ID（递归）
 */
export function getAllSubFolderIds(folderId: string, folders: Folder[]): string[] {
  const subFolderIds: string[] = [];
  
  const findSubFolders = (parentId: string) => {
    folders.forEach(folder => {
      if (folder.parentId === parentId) {
        subFolderIds.push(folder.id);
        findSubFolders(folder.id);
      }
    });
  };
  
  findSubFolders(folderId);
  return subFolderIds;
}

/**
 * 获取文件夹路径（面包屑）
 */
export function getFolderPath(folderId: string | null, folders: Folder[]): Folder[] {
  if (!folderId) return [];
  
  const path: Folder[] = [];
  let currentId: string | null = folderId;
  
  while (currentId) {
    const folder = folders.find(f => f.id === currentId);
    if (!folder) break;
    
    path.unshift(folder);
    currentId = folder.parentId;
  }
  
  return path;
}

/**
 * 计算文件夹统计信息
 */
export function calculateFolderStats(folderId: string, folders: Folder[], notes: NoteWithFolder[]): FolderStats {
  // 直接笔记数量
  const directNoteCount = notes.filter(note => note.folderId === folderId).length;
  
  // 直接子文件夹数量
  const subFolderCount = folders.filter(folder => folder.parentId === folderId).length;
  
  // 获取所有子文件夹ID
  const allSubFolderIds = getAllSubFolderIds(folderId, folders);
  
  // 总笔记数量（包含子文件夹）
  const totalNoteCount = notes.filter(note => 
    note.folderId === folderId || 
    (note.folderId && allSubFolderIds.includes(note.folderId))
  ).length;
  
  return {
    noteCount: directNoteCount,
    subFolderCount,
    totalNoteCount
  };
}

/**
 * 获取根目录笔记数量
 */
export function getRootNotesCount(notes: NoteWithFolder[]): number {
  return notes.filter(note => !note.folderId).length;
}

/**
 * 获取所有笔记数量
 */
export function getTotalNotesCount(notes: NoteWithFolder[]): number {
  return notes.length;
}

/**
 * 查找文件夹
 */
export function findFolder(folderId: string, folders: Folder[]): Folder | undefined {
  return folders.find(folder => folder.id === folderId);
}

/**
 * 获取文件夹中的笔记
 */
export function getNotesInFolder(folderId: string | null, notes: NoteWithFolder[]): NoteWithFolder[] {
  return notes.filter(note => note.folderId === folderId);
}

/**
 * 生成唯一的文件夹ID
 */
export function generateFolderId(): string {
  return 'folder_' + Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 检查文件夹是否为空
 */
export function isFolderEmpty(folderId: string, folders: Folder[], notes: NoteWithFolder[]): boolean {
  // 检查是否有笔记
  const hasNotes = notes.some(note => note.folderId === folderId);
  if (hasNotes) return false;
  
  // 检查是否有子文件夹
  const hasSubFolders = folders.some(folder => folder.parentId === folderId);
  return !hasSubFolders;
}

/**
 * 扁平化文件夹树
 */
export function flattenFolderTree(nodes: FolderTreeNode[]): Folder[] {
  const result: Folder[] = [];
  
  const flatten = (nodes: FolderTreeNode[]) => {
    nodes.forEach(node => {
      result.push(node.folder);
      if (node.children.length > 0) {
        flatten(node.children);
      }
    });
  };
  
  flatten(nodes);
  return result;
}