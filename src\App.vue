<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import Main from './views/Main.vue';
import Todos from './views/Todos.vue';

const route = ref('');
const enterAction = ref({});

onMounted(() => {
  window.utools.onPluginEnter((action) => {
    route.value = action.code;
    enterAction.value = action;
  });
  window.utools.onPluginOut((isKill) => {
    route.value = '';
  });
});
</script>

<template>
  <template v-if="route === 'hello'">
    <Main :enterAction="enterAction"></Main>
  </template>
  <template v-if="route === 'read'">
    <Main :enterAction="enterAction"></Main>
  </template>
  <template v-if="route === 'write'">
    <Main :enterAction="enterAction"></Main>
  </template>
  <template v-if="route === 'todo'">
    <Todos></Todos>
  </template>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  color: #333;
}
</style>
