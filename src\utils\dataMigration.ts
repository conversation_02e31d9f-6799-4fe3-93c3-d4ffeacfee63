// 数据迁移工具函数

import type { NoteWithFolder } from '../types/folder';

/**
 * 为现有笔记数据添加 folderId 字段
 */
export function migrateNotesData(existingNotes: any[]): NoteWithFolder[] {
  return existingNotes.map(note => ({
    ...note,
    folderId: note.folderId || null // 为现有笔记添加默认值
  }));
}

/**
 * 检查笔记数据是否需要迁移
 */
export function needsNoteMigration(notes: any[]): boolean {
  if (!notes || notes.length === 0) return false;
  
  // 检查是否有笔记缺少 folderId 字段
  return notes.some(note => !note.hasOwnProperty('folderId'));
}

/**
 * 清理孤立的笔记（所属文件夹已被删除）
 */
export function cleanupOrphanedNotes(notes: NoteWithFolder[], folderIds: string[]): NoteWithFolder[] {
  return notes.map(note => {
    // 如果笔记的 folderId 不在现有文件夹列表中，将其移到根目录
    if (note.folderId && !folderIds.includes(note.folderId)) {
      return {
        ...note,
        folderId: null
      };
    }
    return note;
  });
}

/**
 * 验证数据完整性
 */
export function validateDataIntegrity(notes: NoteWithFolder[], folders: any[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  const folderIds = folders.map(f => f.id);
  
  // 检查笔记的文件夹引用
  notes.forEach(note => {
    if (note.folderId && !folderIds.includes(note.folderId)) {
      issues.push(`笔记 "${note.title}" 引用了不存在的文件夹 ID: ${note.folderId}`);
    }
  });
  
  // 检查文件夹的父级引用
  folders.forEach(folder => {
    if (folder.parentId && !folderIds.includes(folder.parentId)) {
      issues.push(`文件夹 "${folder.name}" 引用了不存在的父文件夹 ID: ${folder.parentId}`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * 修复数据完整性问题
 */
export function fixDataIntegrity(notes: NoteWithFolder[], folders: any[]): {
  fixedNotes: NoteWithFolder[];
  fixedFolders: any[];
  fixedIssues: string[];
} {
  const fixedIssues: string[] = [];
  const folderIds = folders.map(f => f.id);
  
  // 修复笔记的文件夹引用
  const fixedNotes = notes.map(note => {
    if (note.folderId && !folderIds.includes(note.folderId)) {
      fixedIssues.push(`将笔记 "${note.title}" 移动到根目录`);
      return {
        ...note,
        folderId: null
      };
    }
    return note;
  });
  
  // 修复文件夹的父级引用
  const fixedFolders = folders.map(folder => {
    if (folder.parentId && !folderIds.includes(folder.parentId)) {
      fixedIssues.push(`将文件夹 "${folder.name}" 移动到根目录`);
      return {
        ...folder,
        parentId: null
      };
    }
    return folder;
  });
  
  return {
    fixedNotes,
    fixedFolders,
    fixedIssues
  };
}