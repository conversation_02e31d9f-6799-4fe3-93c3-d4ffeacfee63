// 文件夹服务类 - 核心文件夹管理功能
import type { 
  Folder, 
  FolderTreeNode, 
  NoteWithFolder, 
  FolderStats, 
  FolderOperationResult,
  CreateFolderParams,
  MoveNoteParams
} from '../types/folder.js';

import { 
  validateCreateFolder, 
  validateUpdateFolder
} from '../utils/folderValidation.js';

export class FolderService {
  private static readonly FOLDERS_KEY = 'folders';
  private static readonly NOTES_KEY = 'notes';

  /**
   * 获取所有文件夹
   */
  static getAllFolders(): Folder[] {
    try {
      const stored = window.utools.dbStorage.getItem(this.FOLDERS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取文件夹失败:', error);
      return [];
    }
  }

  /**
   * 保存文件夹数据
   */
  private static saveFolders(folders: Folder[]): void {
    try {
      window.utools.dbStorage.setItem(this.FOLDERS_KEY, JSON.stringify(folders));
    } catch (error) {
      console.error('保存文件夹失败:', error);
      throw new Error('保存文件夹失败');
    }
  }

  /**
   * 创建文件夹
   */
  static createFolder(params: CreateFolderParams): FolderOperationResult {
    try {
      const folders = this.getAllFolders();
      
      // 验证参数
      const validation = validateCreateFolder(params, folders);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.error || '创建文件夹失败'
        };
      }

      // 创建新文件夹
      const newFolder: Folder = {
        id: Date.now().toString(),
        name: params.name.trim(),
        parentId: params.parentId || null,
        createdDate: new Date().toISOString(),
        updatedDate: new Date().toISOString(),
        color: params.color,
        icon: params.icon
      };

      folders.push(newFolder);
      this.saveFolders(folders);

      return {
        success: true,
        message: '文件夹创建成功',
        data: newFolder
      };
    } catch (error) {
      console.error('创建文件夹失败:', error);
      return {
        success: false,
        message: '创建文件夹失败'
      };
    }
  }  /*
*
   * 删除文件夹
   */
  static deleteFolder(folderId: string): FolderOperationResult {
    try {
      const folders = this.getAllFolders();
      const folderIndex = folders.findIndex(f => f.id === folderId);
      
      if (folderIndex === -1) {
        return {
          success: false,
          message: '文件夹不存在'
        };
      }

      // 获取文件夹中的笔记
      const notes = this.getAllNotes();
      const folderNotes = notes.filter(note => note.folderId === folderId);
      
      // 将文件夹中的笔记移动到根目录
      if (folderNotes.length > 0) {
        folderNotes.forEach(note => {
          note.folderId = null;
        });
        this.saveNotes(notes);
      }

      // 删除子文件夹（递归）
      const childFolders = folders.filter(f => f.parentId === folderId);
      childFolders.forEach(child => {
        this.deleteFolder(child.id);
      });

      // 删除文件夹
      folders.splice(folderIndex, 1);
      this.saveFolders(folders);

      return {
        success: true,
        message: `文件夹已删除${folderNotes.length > 0 ? `，${folderNotes.length}条笔记已移至根目录` : ''}`
      };
    } catch (error) {
      console.error('删除文件夹失败:', error);
      return {
        success: false,
        message: '删除文件夹失败'
      };
    }
  }

  /**
   * 重命名文件夹
   */
  static renameFolder(folderId: string, newName: string): FolderOperationResult {
    try {
      const folders = this.getAllFolders();
      
      // 验证参数
      const validation = validateUpdateFolder(folderId, newName, folders);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.error || '重命名失败'
        };
      }

      const folderIndex = folders.findIndex(f => f.id === folderId);
      if (folderIndex === -1) {
        return {
          success: false,
          message: '文件夹不存在'
        };
      }

      // 更新文件夹名称
      folders[folderIndex].name = newName.trim();
      folders[folderIndex].updatedDate = new Date().toISOString();
      
      this.saveFolders(folders);

      return {
        success: true,
        message: '文件夹重命名成功',
        data: folders[folderIndex]
      };
    } catch (error) {
      console.error('重命名文件夹失败:', error);
      return {
        success: false,
        message: '重命名文件夹失败'
      };
    }
  } 
 /**
   * 获取所有笔记
   */
  private static getAllNotes(): NoteWithFolder[] {
    try {
      const stored = window.utools.dbStorage.getItem(this.NOTES_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取笔记失败:', error);
      return [];
    }
  }

  /**
   * 保存笔记数据
   */
  private static saveNotes(notes: NoteWithFolder[]): void {
    try {
      window.utools.dbStorage.setItem(this.NOTES_KEY, JSON.stringify(notes));
    } catch (error) {
      console.error('保存笔记失败:', error);
      throw new Error('保存笔记失败');
    }
  }

  /**
   * 移动笔记到文件夹
   */
  static moveNoteToFolder(params: MoveNoteParams): FolderOperationResult {
    try {
      const notes = this.getAllNotes();
      const noteIndex = notes.findIndex(note => note.id === params.noteId);
      
      if (noteIndex === -1) {
        return {
          success: false,
          message: '笔记不存在'
        };
      }

      // 如果目标文件夹不为null，验证文件夹是否存在
      if (params.targetFolderId !== null) {
        const folders = this.getAllFolders();
        const targetFolder = folders.find(f => f.id === params.targetFolderId);
        if (!targetFolder) {
          return {
            success: false,
            message: '目标文件夹不存在'
          };
        }
      }

      // 移动笔记
      notes[noteIndex].folderId = params.targetFolderId;
      this.saveNotes(notes);

      const targetName = params.targetFolderId === null ? '根目录' : '文件夹';
      return {
        success: true,
        message: `笔记已移动到${targetName}`
      };
    } catch (error) {
      console.error('移动笔记失败:', error);
      return {
        success: false,
        message: '移动笔记失败'
      };
    }
  }

  /**
   * 获取文件夹中的笔记
   */
  static getNotesInFolder(folderId: string | null): NoteWithFolder[] {
    try {
      const notes = this.getAllNotes();
      return notes.filter(note => note.folderId === folderId);
    } catch (error) {
      console.error('获取文件夹笔记失败:', error);
      return [];
    }
  } 
 /**
   * 构建文件夹树结构
   */
  static getFolderTree(): FolderTreeNode[] {
    try {
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      // 构建文件夹树
      const buildTree = (parentId: string | null): FolderTreeNode[] => {
        return folders
          .filter(folder => folder.parentId === parentId)
          .map(folder => {
            const children = buildTree(folder.id);
            const directNoteCount = notes.filter(note => note.folderId === folder.id).length;
            const childNoteCount = children.reduce((sum, child) => sum + child.noteCount, 0);
            
            return {
              folder,
              children,
              noteCount: directNoteCount + childNoteCount
            };
          })
          .sort((a, b) => a.folder.name.localeCompare(b.folder.name));
      };

      return buildTree(null);
    } catch (error) {
      console.error('构建文件夹树失败:', error);
      return [];
    }
  }

  /**
   * 获取文件夹统计信息
   */
  static getFolderStats(folderId: string): FolderStats {
    try {
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      // 直接笔记数量
      const noteCount = notes.filter(note => note.folderId === folderId).length;
      
      // 子文件夹数量
      const subFolderCount = folders.filter(folder => folder.parentId === folderId).length;
      
      // 计算总笔记数量（包含子文件夹）
      const calculateTotalNotes = (currentFolderId: string): number => {
        const directNotes = notes.filter(note => note.folderId === currentFolderId).length;
        const childFolders = folders.filter(folder => folder.parentId === currentFolderId);
        const childNotes = childFolders.reduce((sum, child) => sum + calculateTotalNotes(child.id), 0);
        return directNotes + childNotes;
      };
      
      const totalNoteCount = calculateTotalNotes(folderId);

      return {
        noteCount,
        subFolderCount,
        totalNoteCount
      };
    } catch (error) {
      console.error('获取文件夹统计失败:', error);
      return {
        noteCount: 0,
        subFolderCount: 0,
        totalNoteCount: 0
      };
    }
  }

  /**
   * 获取文件夹路径（面包屑导航用）
   */
  static getFolderPath(folderId: string): Folder[] {
    try {
      const folders = this.getAllFolders();
      const path: Folder[] = [];
      
      let currentId: string | null = folderId;
      while (currentId) {
        const folder = folders.find(f => f.id === currentId);
        if (!folder) break;
        
        path.unshift(folder);
        currentId = folder.parentId;
      }
      
      return path;
    } catch (error) {
      console.error('获取文件夹路径失败:', error);
      return [];
    }
  }

  /**
   * 数据迁移 - 为现有笔记添加folderId字段
   */
  static migrateNotesData(): FolderOperationResult {
    try {
      const notes = this.getAllNotes();
      let migrated = 0;
      
      notes.forEach(note => {
        if (note.folderId === undefined) {
          note.folderId = null;
          migrated++;
        }
      });
      
      if (migrated > 0) {
        this.saveNotes(notes);
      }
      
      return {
        success: true,
        message: `数据迁移完成，处理了${migrated}条笔记`
      };
    } catch (error) {
      console.error('数据迁移失败:', error);
      return {
        success: false,
        message: '数据迁移失败'
      };
    }
  }

  /**
   * 数据一致性检查
   */
  static checkDataConsistency(): {
    isConsistent: boolean;
    issues: string[];
    fixedIssues: string[];
  } {
    const issues: string[] = [];
    const fixedIssues: string[] = [];
    
    try {
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      // 检查文件夹引用的一致性
      const folderIds = new Set(folders.map(f => f.id));
      
      // 检查笔记中的文件夹引用
      let notesModified = false;
      notes.forEach(note => {
        if (note.folderId && !folderIds.has(note.folderId)) {
          issues.push(`笔记"${note.title}"引用了不存在的文件夹ID: ${note.folderId}`);
          note.folderId = null; // 修复：将笔记移到根目录
          fixedIssues.push(`已将笔记"${note.title}"移动到根目录`);
          notesModified = true;
        }
      });
      
      // 检查文件夹的父级引用
      let foldersModified = false;
      folders.forEach(folder => {
        if (folder.parentId && !folderIds.has(folder.parentId)) {
          issues.push(`文件夹"${folder.name}"引用了不存在的父文件夹ID: ${folder.parentId}`);
          folder.parentId = null; // 修复：将文件夹移到根级别
          fixedIssues.push(`已将文件夹"${folder.name}"移动到根级别`);
          foldersModified = true;
        }
      });
      
      // 检查循环引用
      const checkCircularReference = (folderId: string, visited: Set<string>): boolean => {
        if (visited.has(folderId)) {
          return true; // 发现循环引用
        }
        
        const folder = folders.find(f => f.id === folderId);
        if (!folder || !folder.parentId) {
          return false;
        }
        
        visited.add(folderId);
        return checkCircularReference(folder.parentId, visited);
      };
      
      folders.forEach(folder => {
        if (folder.parentId && checkCircularReference(folder.id, new Set())) {
          issues.push(`文件夹"${folder.name}"存在循环引用`);
          folder.parentId = null; // 修复：断开循环引用
          fixedIssues.push(`已断开文件夹"${folder.name}"的循环引用`);
          foldersModified = true;
        }
      });
      
      // 保存修复后的数据
      if (notesModified) {
        this.saveNotes(notes);
      }
      if (foldersModified) {
        this.saveFolders(folders);
      }
      
      return {
        isConsistent: issues.length === 0,
        issues,
        fixedIssues
      };
    } catch (error) {
      console.error('数据一致性检查失败:', error);
      return {
        isConsistent: false,
        issues: ['数据一致性检查过程中发生错误'],
        fixedIssues: []
      };
    }
  }

  /**
   * 创建数据备份
   */
  static createBackup(): {
    success: boolean;
    message: string;
    backupId?: string;
  } {
    try {
      const timestamp = new Date().toISOString();
      const backupId = `backup_${Date.now()}`;
      
      const backupData = {
        id: backupId,
        timestamp,
        version: '1.0',
        folders: this.getAllFolders(),
        notes: this.getAllNotes(),
        metadata: {
          totalFolders: this.getAllFolders().length,
          totalNotes: this.getAllNotes().length,
          createdBy: 'FolderService'
        }
      };
      
      // 保存备份数据
      window.utools.dbStorage.setItem(`backup_${backupId}`, JSON.stringify(backupData));
      
      // 更新备份列表
      const backups = this.getBackupList();
      backups.push({
        id: backupId,
        timestamp,
        totalFolders: backupData.metadata.totalFolders,
        totalNotes: backupData.metadata.totalNotes
      });
      
      // 只保留最近10个备份
      if (backups.length > 10) {
        const oldBackups = backups.splice(0, backups.length - 10);
        oldBackups.forEach(backup => {
          window.utools.dbStorage.removeItem(`backup_${backup.id}`);
        });
      }
      
      window.utools.dbStorage.setItem('backup_list', JSON.stringify(backups));
      
      return {
        success: true,
        message: '数据备份创建成功',
        backupId
      };
    } catch (error) {
      console.error('创建备份失败:', error);
      return {
        success: false,
        message: '创建备份失败'
      };
    }
  }

  /**
   * 获取备份列表
   */
  static getBackupList(): Array<{
    id: string;
    timestamp: string;
    totalFolders: number;
    totalNotes: number;
  }> {
    try {
      const stored = window.utools.dbStorage.getItem('backup_list');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return [];
    }
  }

  /**
   * 恢复数据备份
   */
  static restoreBackup(backupId: string): {
    success: boolean;
    message: string;
  } {
    try {
      const backupData = window.utools.dbStorage.getItem(`backup_${backupId}`);
      if (!backupData) {
        return {
          success: false,
          message: '备份数据不存在'
        };
      }
      
      const backup = JSON.parse(backupData);
      
      // 验证备份数据格式
      if (!backup.folders || !backup.notes || !Array.isArray(backup.folders) || !Array.isArray(backup.notes)) {
        return {
          success: false,
          message: '备份数据格式无效'
        };
      }
      
      // 创建当前数据的备份
      this.createBackup();
      
      // 恢复数据
      this.saveFolders(backup.folders);
      this.saveNotes(backup.notes);
      
      return {
        success: true,
        message: `数据已从备份恢复 (${backup.timestamp})`
      };
    } catch (error) {
      console.error('恢复备份失败:', error);
      return {
        success: false,
        message: '恢复备份失败'
      };
    }
  }

  /**
   * 删除备份
   */
  static deleteBackup(backupId: string): {
    success: boolean;
    message: string;
  } {
    try {
      // 删除备份数据
      window.utools.dbStorage.removeItem(`backup_${backupId}`);
      
      // 更新备份列表
      const backups = this.getBackupList();
      const updatedBackups = backups.filter(backup => backup.id !== backupId);
      window.utools.dbStorage.setItem('backup_list', JSON.stringify(updatedBackups));
      
      return {
        success: true,
        message: '备份已删除'
      };
    } catch (error) {
      console.error('删除备份失败:', error);
      return {
        success: false,
        message: '删除备份失败'
      };
    }
  }

  /**
   * 获取全局统计信息
   */
  static getGlobalStats(): {
    totalFolders: number;
    totalNotes: number;
    notesInRoot: number;
    notesInFolders: number;
    emptyFolders: number;
    averageNotesPerFolder: number;
  } {
    try {
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      const totalFolders = folders.length;
      const totalNotes = notes.length;
      const notesInRoot = notes.filter(note => !note.folderId).length;
      const notesInFolders = totalNotes - notesInRoot;
      
      // 计算空文件夹数量
      const emptyFolders = folders.filter(folder => {
        const hasNotes = notes.some(note => note.folderId === folder.id);
        const hasSubfolders = folders.some(f => f.parentId === folder.id);
        return !hasNotes && !hasSubfolders;
      }).length;
      
      // 计算平均每个文件夹的笔记数量
      const averageNotesPerFolder = totalFolders > 0 ? Math.round((notesInFolders / totalFolders) * 10) / 10 : 0;
      
      return {
        totalFolders,
        totalNotes,
        notesInRoot,
        notesInFolders,
        emptyFolders,
        averageNotesPerFolder
      };
    } catch (error) {
      console.error('获取全局统计失败:', error);
      return {
        totalFolders: 0,
        totalNotes: 0,
        notesInRoot: 0,
        notesInFolders: 0,
        emptyFolders: 0,
        averageNotesPerFolder: 0
      };
    }
  }

  /**
   * 获取文件夹详细统计信息
   */
  static getFolderDetailedStats(folderId: string): {
    directNotes: number;
    totalNotes: number;
    subFolders: number;
    depth: number;
    isEmpty: boolean;
    lastModified: string | null;
  } {
    try {
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      const folder = folders.find(f => f.id === folderId);
      if (!folder) {
        throw new Error('文件夹不存在');
      }
      
      // 直接笔记数量
      const directNotes = notes.filter(note => note.folderId === folderId).length;
      
      // 子文件夹数量
      const subFolders = folders.filter(f => f.parentId === folderId).length;
      
      // 计算文件夹深度
      let depth = 0;
      let currentFolder = folder;
      while (currentFolder.parentId) {
        depth++;
        const parent = folders.find(f => f.id === currentFolder.parentId);
        if (!parent) break;
        currentFolder = parent;
      }
      
      // 计算总笔记数量（递归）
      const calculateTotalNotes = (id: string): number => {
        const directCount = notes.filter(note => note.folderId === id).length;
        const childFolders = folders.filter(f => f.parentId === id);
        const childCount = childFolders.reduce((sum, child) => sum + calculateTotalNotes(child.id), 0);
        return directCount + childCount;
      };
      
      const totalNotes = calculateTotalNotes(folderId);
      
      // 判断是否为空文件夹
      const isEmpty = directNotes === 0 && subFolders === 0;
      
      // 获取最后修改时间
      const folderNotes = notes.filter(note => note.folderId === folderId);
      const lastModified = folderNotes.length > 0 
        ? folderNotes.reduce((latest, note) => {
            const noteDate = new Date(note.date);
            const latestDate = new Date(latest);
            return noteDate > latestDate ? note.date : latest;
          }, folderNotes[0].date)
        : null;
      
      return {
        directNotes,
        totalNotes,
        subFolders,
        depth,
        isEmpty,
        lastModified
      };
    } catch (error) {
      console.error('获取文件夹详细统计失败:', error);
      return {
        directNotes: 0,
        totalNotes: 0,
        subFolders: 0,
        depth: 0,
        isEmpty: true,
        lastModified: null
      };
    }
  }

  /**
   * 自动数据同步和维护
   */
  static performMaintenance(): {
    success: boolean;
    message: string;
    details: string[];
  } {
    const details: string[] = [];
    
    try {
      // 1. 数据一致性检查
      const consistencyCheck = this.checkDataConsistency();
      if (!consistencyCheck.isConsistent) {
        details.push(`发现 ${consistencyCheck.issues.length} 个数据一致性问题`);
        details.push(...consistencyCheck.fixedIssues);
      } else {
        details.push('数据一致性检查通过');
      }
      
      // 2. 清理过期数据
      const folders = this.getAllFolders();
      const notes = this.getAllNotes();
      
      // 清理空文件夹（可选功能，暂时注释）
      // const emptyFolders = folders.filter(folder => {
      //   const hasNotes = notes.some(note => note.folderId === folder.id);
      //   const hasSubfolders = folders.some(f => f.parentId === folder.id);
      //   return !hasNotes && !hasSubfolders;
      // });
      
      // 3. 创建自动备份
      const backupResult = this.createBackup();
      if (backupResult.success) {
        details.push('自动备份创建成功');
      } else {
        details.push('自动备份创建失败');
      }
      
      // 4. 更新统计信息
      details.push(`当前数据：${folders.length} 个文件夹，${notes.length} 条笔记`);
      
      return {
        success: true,
        message: '数据维护完成',
        details
      };
    } catch (error) {
      console.error('数据维护失败:', error);
      return {
        success: false,
        message: '数据维护失败',
        details: ['维护过程中发生错误']
      };
    }
  }
}