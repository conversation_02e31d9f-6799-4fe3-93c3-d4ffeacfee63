<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  date: {
    type: String,
    required: true
  },
  folderInfo: {
    type: Object,
    default: () => ({ id: null, name: '根目录', path: ['根目录'] })
  },
  isPinned: {
    type: Boolean,
    default: false
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  isBatchMode: {
    type: Boolean,
    default: false
  },
  reminderTime: {
    type: String,
    default: ''
  },
  reminderDate: {
    type: String,
    default: ''
  },
  isExpired: {
    type: Boolean,
    default: false
  },
  isGrouped: {
    type: Boolean,
    default: false
  },
  isFirstInGroup: {
    type: Boolean,
    default: false
  },
  isLastInGroup: {
    type: Boolean,
    default: false
  },
  dataNoteId: {
    type: String,
    default: ''
  },
  layoutMode: {
    type: String,
    default: 'note', // 'note' 或 'todo'
    validator: (value: string) => ['note', 'todo'].includes(value)
  }
});

const emit = defineEmits(['click', 'context-menu', 'toggle-selection', 'drag-start', 'drag-end']);

// 点击卡片
const handleClick = (event: MouseEvent) => {
  emit('click', event);
};

// 右键菜单
const handleContextMenu = (event: MouseEvent) => {
  emit('context-menu', event);
};

// 切换选中状态
const toggleSelection = (event: Event) => {
  emit('toggle-selection', event);
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();

  if (date.getFullYear() === now.getFullYear()) {
    if (date.getMonth() === now.getMonth() && date.getDate() === now.getDate()) {
      return `下午${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
    }
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }

  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};

// 清理HTML标签
const stripHtml = (html: string) => {
  if (!html) return '';
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

// 格式化提醒时间
const formatReminderTime = (date: string, time: string) => {
  if (!date || !time) return '';

  const reminderDate = new Date(`${date}T${time}`);
  const now = new Date();

  // 如果是今天
  if (
    reminderDate.getFullYear() === now.getFullYear() &&
    reminderDate.getMonth() === now.getMonth() &&
    reminderDate.getDate() === now.getDate()
  ) {
    return `今天 ${time}`;
  }

  // 如果是明天
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  if (
    reminderDate.getFullYear() === tomorrow.getFullYear() &&
    reminderDate.getMonth() === tomorrow.getMonth() &&
    reminderDate.getDate() === tomorrow.getDate()
  ) {
    return `明天 ${time}`;
  }

  // 其他日期
  return `${date.replace(/-/g, '/')} ${time}`;
};

// 检查是否有提醒
const hasReminder = (date: string, time: string) => {
  return date && time;
};

// 拖拽相关方法
const handleDragStart = (event: DragEvent) => {
  if (!event.dataTransfer) return;
  
  // 设置拖拽数据
  event.dataTransfer.setData('text/note-id', props.dataNoteId);
  event.dataTransfer.setData('text/note-title', props.title);
  event.dataTransfer.effectAllowed = 'move';
  
  // 创建自定义拖拽图像
  const dragImage = document.createElement('div');
  dragImage.innerHTML = `
    <div style="
      background: #1976d2;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
      max-width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    ">
      📝 ${props.title}
    </div>
  `;
  dragImage.style.position = 'absolute';
  dragImage.style.top = '-1000px';
  document.body.appendChild(dragImage);
  
  event.dataTransfer.setDragImage(dragImage, 0, 0);
  
  // 延迟移除拖拽图像
  setTimeout(() => {
    document.body.removeChild(dragImage);
  }, 0);
  
  // 添加拖拽样式
  const target = event.target as HTMLElement;
  target.classList.add('dragging');
  
  emit('drag-start', props.dataNoteId);
};

const handleDragEnd = (event: DragEvent) => {
  // 恢复样式
  const target = event.target as HTMLElement;
  target.classList.remove('dragging');
  
  emit('drag-end', props.dataNoteId);
};
</script>

<template>
  <div 
    class="note-card" 
    :class="{
      'pinned': isPinned,
      'selected': isSelected,
      'grouped': isGrouped,
      'first-in-group': isFirstInGroup,
      'last-in-group': isLastInGroup,
      'layout-note': layoutMode === 'note',
      'layout-todo': layoutMode === 'todo'
    }" 
    :data-note-id="dataNoteId" 
    draggable="true"
    @click="handleClick" 
    @contextmenu.prevent="handleContextMenu"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
  >

    <!-- 待办模式：单行布局 -->
    <div v-if="layoutMode === 'todo'" class="note-content-row">
      <div v-if="isBatchMode" class="note-checkbox">
        <input type="checkbox" :checked="isSelected" @click.stop @change="$emit('toggle-selection', $event)" />
      </div>

      <div class="note-main-content">
        <span class="note-title">
          <span v-if="isPinned" class="pin-icon">📌</span>
          {{ title }}
        </span>
        <span class="note-separator">-</span>
        <span class="note-preview">{{ stripHtml(content || '').substring(0, 60) }}{{ stripHtml(content || '').length > 60 ? '...' : '' }}</span>
      </div>

      <div class="note-right">
        <span v-if="reminderDate && reminderTime" class="reminder-icon" :class="{ 'expired': isExpired }">⏰</span>
        <span class="note-date">{{ formatDate(date) }}</span>
      </div>
      <div v-if="folderInfo && folderInfo.name !== '根目录'" class="note-folder-right">
        <span class="note-folder">📁{{ folderInfo.name }}</span>
      </div>
    </div>

    <!-- 笔记模式：传统多行布局 -->
    <div v-else class="note-traditional-content">
      <div v-if="isBatchMode" class="note-checkbox note-checkbox-traditional">
        <input type="checkbox" :checked="isSelected" @click.stop @change="$emit('toggle-selection', $event)" />
      </div>
      <h3 class="note-title-traditional">
        <span v-if="isPinned" class="pin-icon">📌</span>
        {{ title }}
      </h3>
      <p class="note-preview-traditional">{{ stripHtml(content || '').substring(0, 80) }}{{ stripHtml(content || '').length > 80 ? '...' : '' }}</p>
      <div class="note-footer">
        <div class="note-footer-left">
          <span class="note-date">{{ formatDate(date) }}</span>
          <span v-if="reminderDate && reminderTime" class="reminder-icon" :class="{ 'expired': isExpired }">⏰</span>
        </div>
        <div v-if="folderInfo && folderInfo.name !== '根目录'" class="note-footer-right">
          <span class="note-folder">📁{{ folderInfo.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.note-card {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
}

/* 待办模式样式 */
.note-card.layout-todo {
  padding: 8px 12px;
  margin-bottom: 4px;
  min-height: auto;
}

/* 笔记模式样式 */
.note-card.layout-note {
  padding: 12px;
  margin-bottom: 8px;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.note-card.selected {
  background-color: #f0f4ff;
  border-color: #1976d2;
}

.note-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* 笔记模式悬停效果 */
.note-card.layout-note:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.note-card.pinned {
  background-color: #fff8e1;
  border-left: 3px solid #ff9800;
}

/* 新的单行布局样式 */
.note-content-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
  position: relative;
  padding-right: 60px; /* 为文件夹信息预留空间 */
}

.note-checkbox {
  position: relative;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* 笔记模式下的复选框 */
.note-checkbox-traditional {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 16px;
  height: 16px;
  z-index: 10;
}

.note-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.note-checkbox label {
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 3px;
  border: 1.5px solid #ccc;
  background-color: #fff;
  cursor: pointer;
}

.note-checkbox input[type="checkbox"]:checked+label {
  background-color: #4caf50;
  border-color: #4caf50;
}

.note-checkbox input[type="checkbox"]:checked+label:after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.note-main-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
  overflow: hidden;
  min-width: 0;
}

.note-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.pin-icon {
  margin-right: 4px;
  font-size: 10px;
}

/* 笔记模式下的置顶图标 */
.note-title-traditional .pin-icon {
  margin-right: 4px;
  font-size: 12px;
}

.note-separator {
  color: #ccc;
  font-size: 12px;
  flex-shrink: 0;
}

.note-preview {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.note-right {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.note-date {
  font-size: 10px;
  color: #999;
  font-weight: 400;
  white-space: nowrap;
}

.note-folder {
  font-size: 9px;
  color: #666;
  font-weight: 400;
  white-space: nowrap;
  opacity: 0.8;
}

.note-folder-right {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 3px;
}

/* 传统笔记布局样式 */
.note-traditional-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.note-title-traditional {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  color: #1a1a1a;
  line-height: 1.2;
  letter-spacing: -0.01em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.note-preview-traditional {
  font-size: 12px;
  color: #8a8a8a;
  margin-bottom: 8px;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 400;
  word-break: break-word;
  hyphens: auto;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  width: 100%;
}

.note-footer-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.note-footer-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.note-footer .note-date {
  font-size: 10px;
  color: #b0b0b0;
  font-weight: 400;
}

.note-footer .note-folder {
  font-size: 9px;
  color: #999;
  font-weight: 400;
  white-space: nowrap;
  opacity: 0.7;
}

.note-footer .reminder-icon {
  margin-left: auto;
  font-size: 12px;
}

.note-card.has-reminder {
  border-right: 2px solid #4caf50;
}

.note-card.reminder-expired {
  border-right: 2px solid #f44336;
}

.note-reminder {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 11px;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 1px 4px;
  border-radius: 8px;
}

.note-reminder.expired {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.reminder-icon {
  font-size: 10px;
  flex-shrink: 0;
}

/* 分组样式 */
.note-card.grouped {
  margin-bottom: 1px;
  border-radius: 0;
  box-shadow: none;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
}

.note-card.first-in-group {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  margin-top: 4px;
  border-top: 1px solid #eee;
}

.note-card.last-in-group {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  margin-bottom: 4px;
  border-bottom: 1px solid #eee;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.note-card.grouped:not(.first-in-group) {
  border-top: 1px dashed #eee;
}

/* 拖拽状态样式 */
.note-card.dragging {
  opacity: 0.6;
  transform: rotate(2deg) scale(0.95);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
  border: 2px solid #1976d2;
  z-index: 1000;
  cursor: grabbing;
}

.note-card.dragging * {
  pointer-events: none;
}

/* 拖拽动画 */
.note-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.note-card.dragging {
  transition: none;
}
</style>