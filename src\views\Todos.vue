<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import AppHeader from '../components/AppHeader.vue';
import NoteCard from '../components/NoteCard.vue';
import ContextMenu from '../components/ContextMenu.vue';
import Editor from '../components/Editor.vue';
import AddButton from '../components/AddButton.vue';
import Notification from '../components/Notification.vue';
import ConfirmDialog from '../components/ConfirmDialog.vue';
import { QuillEditor } from '@vueup/vue-quill';
import {
  MagnifyingGlassIcon as IconSearch,
  XMarkIcon as IconCancel,
  CheckIcon as IconSave
} from '@heroicons/vue/24/outline';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const onEditorReady = (quill: any) => {
  console.log('[DEBUG-TODO] Editor is ready. Attaching click listener.');
  quill.root.addEventListener('click', (e: MouseEvent) => {
    console.log('[DEBUG-TODO] Editor clicked. Event target:', e.target);

    let target = e.target as HTMLElement | null;
    if (target && target.tagName !== 'A') {
      target = target.closest('a');
      console.log('[DEBUG-TODO] Target is not A tag, trying closest("a"):', target);
    }
    
    if (target && target.tagName === 'A') {
      console.log('[DEBUG-TODO] A tag found:', target);
      let href = target.getAttribute('href');
      console.log('[DEBUG-TODO] Href attribute:', href);
      if (href) {
        e.preventDefault();
        console.log('[DEBUG-TODO] Href found. Checking for utools API...');

        if (!/^https/i.test(href) && !/^http/i.test(href)) {
          href = 'https://' + href;
          console.log('[DEBUG-TODO] Href prepended with https:', href);
        }
        
        if (typeof window !== 'undefined' && window.utools) {
          console.log('[DEBUG-TODO] utools API found. Calling shellOpenExternal...');
          window.utools.shellOpenExternal(href);
        } else {
          console.log('[DEBUG-TODO] utools API not found. Falling back to window.open.');
          window.open(href, '_blank');
        }
      }
    }
  }, true);
};

// 待办项类型定义
interface SubTodo {
  id: string;
  title: string;
  completed: boolean;
  date: string;
  parentId: string;
  content?: string;
  children?: SubTodo[];
}

interface Todo {
  id: string;
  title: string;
  content: string;
  date: string;
  isPinned?: boolean;
  reminderTime?: string;
  reminderDate?: string;
  isExpired?: boolean;
  groupId?: string;
  parentId?: string;
  children?: SubTodo[];
}

interface TodoWithGroupInfo extends Todo {
  isGrouped: boolean;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
}

// 定义ItemType类型，与Editor组件兼容
interface ItemType {
  id: string;
  title: string;
  content?: string;
  completed?: boolean;
  date?: string;
  parentId?: string;
  reminderDate?: string;
  reminderTime?: string;
}

// 待办状态
const todos = ref<string>('');
const title = ref<string>('');
const searchQuery = ref<string>('');
const showEditor = ref<boolean>(false);

// 富文本编辑器相关状态
const isEditing = ref<boolean>(false);
const editingNote = ref<{
  id: string;
  title: string;
  content: string;
  reminderDate?: string;
  reminderTime?: string;
}>({
  id: '',
  title: '',
  content: ''
});

const todoQuillEditorRef = ref<any>(null);

watch(todoQuillEditorRef, (newRef) => {
  if (newRef) {
    const quill = newRef.getQuill();
    if (quill) {
      console.log('[DEBUG-TODO] Quill instance obtained via ref. Attaching listener.');
      quill.root.addEventListener('click', (e: MouseEvent) => {
        let target = e.target as HTMLElement | null;
        if (target && target.tagName !== 'A') {
          target = target.closest('a');
        }
        
        if (target && target.tagName === 'A') {
          let href = target.getAttribute('href');
          if (href) {
            e.preventDefault();
            if (!/^https?/i.test(href)) {
              href = 'https://' + href;
            }
            if (typeof window !== 'undefined' && window.utools) {
              window.utools.shellOpenExternal(href);
            } else {
              window.open(href, '_blank');
            }
          }
        }
      }, true);
    }
  }
});

// 富文本编辑器搜索功能状态
const editorSearchQuery = ref('');
const showEditorSearch = ref(false);
const searchMatches = ref<number[]>([]);
const currentMatchIndex = ref(-1);

// 修改待办状态的类型定义
const savedTodos = ref<Array<{
  id: string,
  title: string,
  content: string,
  date: string,
  isPinned?: boolean,
  reminderTime?: string,
  reminderDate?: string,
  isExpired?: boolean,
  groupId?: string,
  parentId?: string, // 添加父级ID，用于构建树形结构
  children?: Array<{  // 子待办项
    id: string,
    title: string,
    completed: boolean,
    date: string,
    parentId: string, // 父级ID
    children?: Array<any> // 支持多级嵌套
  }>
}>>([]);
const editingTodoId = ref<string>('');

// 通知状态
const notification = ref<{ show: boolean, message: string, type: 'success' | 'error' | 'info' }>({
  show: false,
  message: '',
  type: 'info'
});

// 确认对话框状态
const confirmDialog = ref<{
  show: boolean,
  message: string,
  onConfirm: () => void,
  onCancel: () => void
}>({
  show: false,
  message: '',
  onConfirm: () => { },
  onCancel: () => { }
});

// 右键菜单状态
const showContextMenu = ref<boolean>(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const selectedTodoIndex = ref<number>(-1);
const selectedTodoId = ref<string>('');
const isBatchDeleteMode = ref<boolean>(false);
const selectedTodos = ref<Set<number>>(new Set());



// 拖拽排序相关状态
const draggedTodoId = ref<string | null>(null);
const draggedSubTodoId = ref<string | null>(null);
const dragOverTodoId = ref<string | null>(null);
const dragOverSubTodoId = ref<string | null>(null);

// 子项批量操作状态
const isSubTodoBatchMode = ref<boolean>(false);
const selectedSubTodos = ref<Map<string, Set<string>>>(new Map());

// 添加紧凑视图模式状态
const isCompactMode = ref<boolean>(true); // 默认启用紧凑视图

// 跟踪展开的待办子项
const expandedTodos = ref<Set<string>>(new Set()); // 存储已展开的待办ID

// 切换待办子项的展开/收起状态
const toggleExpandTodo = (todoId: string) => {
  if (expandedTodos.value.has(todoId)) {
    expandedTodos.value.delete(todoId);
  } else {
    expandedTodos.value.add(todoId);
  }
};

// 显示通知
const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  notification.value = {
    show: true,
    message,
    type
  };

  // 3秒后自动关闭通知
  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};

// 显示确认对话框
const showConfirmDialog = (message: string, onConfirm: () => void, onCancel: () => void = () => { }) => {
  confirmDialog.value = {
    show: true,
    message,
    onConfirm,
    onCancel
  };
};

// 关闭确认对话框
const closeConfirmDialog = () => {
  confirmDialog.value.show = false;
};

// 切换批量删除模式
const toggleBatchDelete = () => {
  isBatchDeleteMode.value = !isBatchDeleteMode.value;
  selectedTodos.value.clear();
};

// 切换待办选中状态
const toggleTodoSelection = (index: number, event: Event) => {
  if (!isBatchDeleteMode.value) return;
  event.stopPropagation();
  if (selectedTodos.value.has(index)) {
    selectedTodos.value.delete(index);
  } else {
    selectedTodos.value.add(index);
  }
};

// 确认批量删除
const confirmBatchDelete = () => {
  if (selectedTodos.value.size === 0) {
    showNotification('请至少选择一条待办', 'error');
    return;
  }

  showConfirmDialog(
    `确定要删除选中的 ${selectedTodos.value.size} 条待办吗？`,
    () => {
      const sortedIndices = Array.from(selectedTodos.value).sort((a, b) => b - a);
      sortedIndices.forEach(index => {
        savedTodos.value.splice(index, 1);
      });
      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      isBatchDeleteMode.value = false;
      selectedTodos.value.clear();
      showNotification(`已删除 ${sortedIndices.length} 条待办`, 'info');
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 从本地存储加载待办
onMounted(() => {
  // 加载保存的待办列表
  const savedTodosData = utools.dbStorage.getItem('todos');
  if (savedTodosData) {
    try {
      savedTodos.value = JSON.parse(savedTodosData);
      // 检查是否有过期提醒
      checkForExpiredReminders();
    } catch (e) {
      console.error('Error parsing saved todos:', e);
      savedTodos.value = [];
    }
  }

  // 加载紧凑视图设置
  const compactModeData = utools.dbStorage.getItem('compactTodoMode');
  if (compactModeData !== null) {
    isCompactMode.value = compactModeData === 'true';
  }

  // 默认不展开任何待办项
  // 如果需要展开，用户可以手动点击展开按钮

  // 设置定时检查过期提醒的间隔
  reminderCheckInterval = setInterval(checkForExpiredReminders, 60000); // 每分钟检查一次

  // 添加全局点击事件监听器，用于关闭右键菜单
  document.addEventListener('click', () => {
    showContextMenu.value = false;
  });
});

// 过滤并排序待办
const filteredTodos = computed(() => {
  let todos = [...savedTodos.value];

  // 如果有搜索查询，先过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    todos = todos.filter(todo =>
      todo.title.toLowerCase().includes(query) ||
      todo.content.toLowerCase().includes(query) ||
      // 搜索子项内容
      (todo.children && todo.children.some(child =>
        child.title.toLowerCase().includes(query)
      ))
    );
  }

  // 先按分组排序，再按置顶状态排序
  return todos.sort((a, b) => {
    // 首先按置顶状态排序
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;

    // 然后按分组排序，同一分组的待办放在一起
    if (a.groupId && b.groupId) {
      if (a.groupId === b.groupId) return 0;
      return a.groupId > b.groupId ? 1 : -1;
    }
    if (a.groupId && !b.groupId) return -1;
    if (!a.groupId && b.groupId) return 1;

    // 最后按创建时间排序（新的在前）
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });
});

// 计算分组信息
const todosWithGroupInfo = computed(() => {
  const todos = filteredTodos.value;
  const result: TodoWithGroupInfo[] = [];

  // 按分组ID对待办进行分组
  const groupedByGroupId: Record<string, Todo[]> = {};

  todos.forEach(todo => {
    if (todo.groupId) {
      if (!groupedByGroupId[todo.groupId]) {
        groupedByGroupId[todo.groupId] = [];
      }
      groupedByGroupId[todo.groupId].push(todo);
    } else {
      // 没有分组ID的待办单独处理
      result.push({
        ...todo,
        isGrouped: false,
        isFirstInGroup: false,
        isLastInGroup: false
      });
    }
  });

  // 处理分组内的待办
  Object.values(groupedByGroupId).forEach(group => {
    if (Array.isArray(group) && group.length > 0) {
      // 按创建时间排序
      group.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // 添加分组信息
      group.forEach((todo, index) => {
        result.push({
          ...todo,
          isGrouped: true,
          isFirstInGroup: index === 0,
          isLastInGroup: index === group.length - 1
        });
      });
    }
  });

  return result;
});

// 保存待办
const saveTodo = (data: { id: string, title: string, items?: any[] }) => {
  if (!data.title.trim()) {
    showNotification('标题不能为空！', 'error');
    return;
  }
  console.log('保存待办数据:', data);
  const now = new Date().toLocaleString();

  if (editingTodoId.value) {
    // 更新现有待办
    const index = savedTodos.value.findIndex(todo => todo.id === editingTodoId.value);
    if (index !== -1) {
      // 获取当前待办以保留现有子待办项的内容
      const currentTodo = savedTodos.value[index];
      const currentChildren = currentTodo.children || [];

      // 创建新的子待办项数组
      const updatedChildren = data.items?.map(item => {
        // 检查是否已有相同ID的子待办，如果有则保留其content
        const existingChild = currentChildren.find(child => child.id === item.id);
        return {
          id: item.id || Date.now().toString() + Math.random().toString(36).substr(2, 5),
          title: typeof item.title === 'object' ? item.title.title : item.title,
          content: item.content || (existingChild ? (existingChild as any).content : ''),
          completed: item.completed !== undefined ? item.completed : false,
          date: now,
          parentId: editingTodoId.value
        };
      }) || currentChildren;

      savedTodos.value[index] = {
        ...savedTodos.value[index],
        title: data.title,
        date: now,
        children: updatedChildren
      };

      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      showNotification('待办已更新', 'success');
    }
  } else {
    // 创建新待办
    const newTodoId = Date.now().toString();
    const newTodo = {
      id: newTodoId,
      title: data.title,
      content: '',
      date: now,
      isPinned: false,
      children: data.items?.map(item => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
        title: typeof item.title === 'object' ? item.title.title : item.title,
        content: item.content || '',
        completed: false,
        date: now,
        parentId: newTodoId
      })) || []
    };

    savedTodos.value.push(newTodo);
    utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
    showNotification('待办已保存', 'success');
  }

  showEditor.value = false;
  editingTodoId.value = '';
};

// 加载待办
const loadTodo = (todo: any) => {
  console.log('Loading todo:', todo);

  // 如果是子待办项（有parentId属性）
  if (todo.parentId) {
    // 打开富文本编辑器
    isEditing.value = true;
    editingNote.value = {
      id: todo.id,
      title: typeof todo.title === 'object' ? todo.title.title : todo.title,
      content: typeof todo.title === 'object' ? todo.title.content : (todo.content || '')
    };
    return;
  }

  // 否则是常规待办项，打开标准编辑器
  editingTodoId.value = todo.id;
  title.value = todo.title;
  todos.value = todo.content || '';
  showEditor.value = true;
};

// 快速添加子项
const quickAddSubTodo = (parentId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  // 显示输入对话框
  const subTodoTitle = prompt('请输入子待办标题:');
  if (!subTodoTitle || !subTodoTitle.trim()) return;

  // 创建子待办 - 使用统一格式
  const newSubTodo = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
    title: subTodoTitle,  // 直接使用字符串作为title
    content: '',  // 显式添加content字段
    completed: false,
    date: new Date().toLocaleString(),
    parentId: parentId
  };

  // 确保children数组存在
  if (!savedTodos.value[parentIndex].children) {
    savedTodos.value[parentIndex].children = [];
  }

  // 添加到子待办列表
  savedTodos.value[parentIndex].children.push(newSubTodo);
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('子待办已添加', 'success');
};

// 批量添加子项
const batchAddSubTodos = (parentId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  // 显示多行输入对话框
  const input = prompt('请输入多个子待办，每行一个:');
  if (!input || !input.trim()) return;

  // 按行分割输入
  const titles = input.split('\n').filter(line => line.trim());
  if (titles.length === 0) return;

  // 确保children数组存在
  if (!savedTodos.value[parentIndex].children) {
    savedTodos.value[parentIndex].children = [];
  }

  // 批量添加子待办
  const now = new Date().toLocaleString();
  titles.forEach(title => {
    const newSubTodo: SubTodo = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
      title: title.trim(),  // 直接使用字符串作为title
      content: '',  // 显式添加content字段
      completed: false,
      date: now,
      parentId: parentId
    };
    savedTodos.value[parentIndex].children?.push(newSubTodo);
  });

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification(`已添加 ${titles.length} 个子待办`, 'success');
};

// 清空所有子项
const clearAllSubTodos = (parentId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  // 如果没有子项，直接返回
  if (!savedTodos.value[parentIndex].children || savedTodos.value[parentIndex].children.length === 0) {
    showNotification('没有子待办可清空', 'info');
    return;
  }

  // 显示确认对话框
  showConfirmDialog(
    `确定要清空所有子待办吗？共 ${savedTodos.value[parentIndex].children.length} 项`,
    () => {
      // 清空子待办
      savedTodos.value[parentIndex].children = [];
      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      showNotification('所有子待办已清空', 'info');
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 完成所有子项
const completeAllSubTodos = (parentId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  // 如果没有子项，直接返回
  if (!savedTodos.value[parentIndex].children || savedTodos.value[parentIndex].children.length === 0) {
    showNotification('没有子待办可完成', 'info');
    return;
  }

  // 将所有子待办标记为已完成
  savedTodos.value[parentIndex].children.forEach(child => {
    child.completed = true;

    // 递归完成嵌套子待办
    if (child.children && child.children.length > 0) {
      child.children.forEach(nestedChild => {
        nestedChild.completed = true;
      });
    }
  });

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('所有子待办已标记为完成', 'success');
};

// 删除待办
const deleteTodo = () => {
  // 使用ID查找正确的待办
  const index = savedTodos.value.findIndex(todo => todo.id === selectedTodoId.value);
  if (index === -1) return;

  showConfirmDialog(
    '确定要删除这条待办吗？',
    () => {
      savedTodos.value.splice(index, 1);
      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      showContextMenu.value = false;
      showNotification('待办已删除', 'info');
      closeConfirmDialog();
    },
    () => {
      showContextMenu.value = false;
      closeConfirmDialog();
    }
  );
};

// 置顶/取消置顶待办
const togglePinTodo = () => {
  // 使用ID查找正确的待办
  const index = savedTodos.value.findIndex(todo => todo.id === selectedTodoId.value);
  if (index === -1) return;

  const todo = savedTodos.value[index];
  todo.isPinned = !todo.isPinned;
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showContextMenu.value = false;
  showNotification(todo.isPinned ? '待办已置顶' : '待办已取消置顶', 'success');
};

// 创建新待办
const createNewTodo = () => {
  title.value = '';
  todos.value = '';
  editingTodoId.value = ''; // 重置编辑中的待办ID
  showEditor.value = true;
};



// 检查提醒状态
const checkForExpiredReminders = () => {
  const now = new Date();
  let hasUpdates = false;

  savedTodos.value.forEach(todo => {
    if (todo.reminderDate && todo.reminderTime) {
      const reminderDateTime = new Date(`${todo.reminderDate}T${todo.reminderTime}`);
      const wasExpired = todo.isExpired;
      const isNowExpired = reminderDateTime < now;

      // 如果状态发生变化，更新待办
      if (wasExpired !== isNowExpired) {
        todo.isExpired = isNowExpired;
        hasUpdates = true;
      }
    }
  });

  if (hasUpdates) {
    utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  }
};

// 定期检查提醒状态
let reminderCheckInterval: number;

// 显示右键菜单
const showTodoContextMenu = (event: MouseEvent, todo: Todo) => {
  event.preventDefault();
  // 存储待办的ID而不是索引
  selectedTodoId.value = todo.id;
  // 为了向后兼容，仍然计算并保存索引
  selectedTodoIndex.value = savedTodos.value.findIndex(item => item.id === todo.id);
  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  };
  showContextMenu.value = true;
};

// 显示子项管理菜单
const showSubTodoMenu = (event: MouseEvent, parentId: string) => {
  event.preventDefault();
  event.stopPropagation();

  // 存储待办的ID
  selectedTodoId.value = parentId;
  // 为了向后兼容，计算并保存索引
  selectedTodoIndex.value = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (selectedTodoIndex.value === -1) return;

  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  };

  // 显示特定的子项管理菜单
  showContextMenu.value = true;
  isSubTodoMenu.value = true;
};

// 添加一个标志来区分普通右键菜单和子项管理菜单
const isSubTodoMenu = ref<boolean>(false);

// 处理子项管理菜单选项
const handleSubTodoAction = (action: string) => {
  // 使用ID查找正确的待办
  const index = savedTodos.value.findIndex(todo => todo.id === selectedTodoId.value);
  if (index === -1) return;

  const todo = savedTodos.value[index];

  switch (action) {
    case 'quick-add':
      quickAddSubTodo(todo.id);
      break;
    case 'batch-add':
      batchAddSubTodos(todo.id);
      break;
    case 'complete-all':
      completeAllSubTodos(todo.id);
      break;
    case 'clear-all':
      clearAllSubTodos(todo.id);
      break;
  }

  showContextMenu.value = false;
  isSubTodoMenu.value = false;
};

// 取消编辑
const cancelEdit = () => {
  title.value = '';
  todos.value = '';
  showEditor.value = false;
};

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query;
};



// 添加子待办项
const addSubTodo = (parentId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  // 显示输入对话框
  const subTodoTitle = prompt('请输入子待办标题:');
  if (!subTodoTitle || !subTodoTitle.trim()) return;

  // 创建子待办
  const newSubTodo = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
    title: subTodoTitle,
    completed: false,
    date: new Date().toLocaleString(),
    parentId: parentId
  };

  // 确保children数组存在
  if (!savedTodos.value[parentIndex].children) {
    savedTodos.value[parentIndex].children = [];
  }

  // 添加到子待办列表
  savedTodos.value[parentIndex].children.push(newSubTodo);
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('子待办已添加', 'success');
};

// 删除子待办
const deleteSubTodo = (parentIndex: number, subIndex: number) => {
  if (parentIndex < 0 || parentIndex >= savedTodos.value.length) return;
  const parent = savedTodos.value[parentIndex];
  if (!parent.children) return;

  showConfirmDialog(
    '确定要删除这条子待办吗？',
    () => {
      parent.children?.splice(subIndex, 1);
      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      showNotification('子待办已删除', 'info');
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 保存子待办状态
const saveSubTodoStatus = (parentIndex: number, subIndex: number) => {
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
};

// 添加嵌套子待办
const addNestedTodo = (parentId: string, subTodoId: string) => {
  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1) return;

  const parent = savedTodos.value[parentIndex];
  if (!parent.children) return;

  // 找到子待办
  const subTodoIndex = parent.children.findIndex(child => child.id === subTodoId);
  if (subTodoIndex === -1) return;

  const subTodo = parent.children[subTodoIndex];

  // 显示输入对话框
  const nestedTodoTitle = prompt('请输入嵌套子待办标题:');
  if (!nestedTodoTitle || !nestedTodoTitle.trim()) return;

  // 创建嵌套子待办 - 使用统一格式
  const newNestedTodo: SubTodo = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
    title: nestedTodoTitle.trim(),
    content: '', // 显式添加content字段
    completed: false,
    date: new Date().toLocaleString(),
    parentId: subTodoId
  };

  // 确保子待办的children数组存在
  if (!subTodo.children) {
    subTodo.children = [];
  }

  // 添加到嵌套子待办列表
  subTodo.children.push(newNestedTodo);
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('嵌套子待办已添加', 'success');
};

// 删除嵌套子待办
const deleteNestedTodo = (parentIndex: number, subIndex: number, nestedIndex: number) => {
  if (parentIndex < 0 || parentIndex >= savedTodos.value.length) return;

  const parent = savedTodos.value[parentIndex];
  if (!parent.children || parent.children.length === 0) return;

  if (subIndex < 0 || subIndex >= parent.children.length) return;

  const subTodo = parent.children[subIndex];
  if (!subTodo.children || subTodo.children.length === 0) return;

  if (nestedIndex < 0 || nestedIndex >= subTodo.children.length) return;

  showConfirmDialog(
    '确定要删除这条嵌套子待办吗？',
    () => {
      if (subTodo.children) {
        subTodo.children.splice(nestedIndex, 1);
        utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
        showNotification('嵌套子待办已删除', 'info');
      }
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 保存嵌套子待办状态
const saveNestedTodoStatus = (parentIndex: number, subIndex: number, nestedIndex: number) => {
  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
};

// 递归获取所有子待办数量
const getTotalSubTodosCount = (todo: any): number => {
  let count = 0;

  // 计算直接子待办数量
  if (todo.children && todo.children.length > 0) {
    count += todo.children.length;

    // 递归计算嵌套子待办数量
    todo.children.forEach((child: any) => {
      if (child.children && child.children.length > 0) {
        count += getTotalSubTodosCount(child);
      }
    });
  }

  return count;
};

// 递归获取已完成子待办数量
const getCompletedSubTodosCount = (todo: any): number => {
  let count = 0;

  // 计算直接子待办中已完成的数量
  if (todo.children && todo.children.length > 0) {
    count += todo.children.filter((child: any) => child.completed).length;

    // 递归计算嵌套子待办中已完成的数量
    todo.children.forEach((child: any) => {
      if (child.children && child.children.length > 0) {
        count += getCompletedSubTodosCount(child);
      }
    });
  }

  return count;
};

// 计算待办完成进度
const getTodoProgress = (todo: any): number => {
  const total = getTotalSubTodosCount(todo);
  if (total === 0) return 0;

  const completed = getCompletedSubTodosCount(todo);
  return Math.round((completed / total) * 100);
};

// 在组件卸载时清除定时器
onUnmounted(() => {
  if (reminderCheckInterval) {
    clearInterval(reminderCheckInterval);
  }
});

// 开始拖拽待办项
const startDragTodo = (todoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value) return;

  draggedTodoId.value = todoId;
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', todoId);
  }
};

// 开始拖拽子待办项
const startDragSubTodo = (parentId: string, subTodoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value) return;

  draggedTodoId.value = parentId;
  draggedSubTodoId.value = subTodoId;
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', subTodoId);
  }
};

// 拖拽经过待办项
const dragOverTodo = (todoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value || draggedTodoId.value === null) return;

  event.preventDefault();
  dragOverTodoId.value = todoId;
};

// 拖拽经过子待办项
const dragOverSubTodo = (parentId: string, subTodoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value || draggedTodoId.value === null) return;

  event.preventDefault();
  dragOverTodoId.value = parentId;
  dragOverSubTodoId.value = subTodoId;
};

// 放置待办项
const dropTodo = (targetTodoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value || draggedTodoId.value === null) return;

  event.preventDefault();

  // 如果是同一个待办项，不做任何操作
  if (draggedTodoId.value === targetTodoId) {
    resetDragState();
    return;
  }

  // 如果是拖拽子待办项
  if (draggedSubTodoId.value !== null) {
    moveSubTodoToAnotherParent(draggedTodoId.value, draggedSubTodoId.value, targetTodoId);
  } else {
    // 如果是拖拽顶级待办项
    moveTodo(draggedTodoId.value, targetTodoId);
  }

  resetDragState();
};

// 放置子待办项
const dropSubTodo = (parentId: string, targetSubTodoId: string, event: DragEvent) => {
  if (isBatchDeleteMode.value || draggedTodoId.value === null) return;

  event.preventDefault();

  // 如果是拖拽子待办项
  if (draggedSubTodoId.value !== null) {
    // 如果是同一个子待办项，不做任何操作
    if (draggedSubTodoId.value === targetSubTodoId) {
      resetDragState();
      return;
    }

    // 如果是同一个父待办项下的子待办项
    if (draggedTodoId.value === parentId) {
      moveSubTodo(parentId, draggedSubTodoId.value, targetSubTodoId);
    } else {
      // 如果是不同父待办项下的子待办项
      moveSubTodoToAnotherParent(draggedTodoId.value, draggedSubTodoId.value, parentId, targetSubTodoId);
    }
  } else {
    // 如果是拖拽顶级待办项到子待办项，将顶级待办项转换为子待办项
    convertTodoToSubTodo(draggedTodoId.value, parentId, targetSubTodoId);
  }

  resetDragState();
};

// 结束拖拽
const endDrag = () => {
  resetDragState();
};

// 重置拖拽状态
const resetDragState = () => {
  draggedTodoId.value = null;
  draggedSubTodoId.value = null;
  dragOverTodoId.value = null;
  dragOverSubTodoId.value = null;
};

// 移动待办项
const moveTodo = (fromTodoId: string, toTodoId: string) => {
  const fromIndex = savedTodos.value.findIndex(todo => todo.id === fromTodoId);
  const toIndex = savedTodos.value.findIndex(todo => todo.id === toTodoId);

  if (fromIndex === -1 || toIndex === -1) return;

  // 移动待办项
  const [movedTodo] = savedTodos.value.splice(fromIndex, 1);
  savedTodos.value.splice(toIndex, 0, movedTodo);

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('待办已移动', 'success');
};

// 移动子待办项
const moveSubTodo = (parentId: string, fromSubTodoId: string, toSubTodoId: string) => {
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1 || !savedTodos.value[parentIndex].children) return;

  const fromIndex = savedTodos.value[parentIndex].children.findIndex(subTodo => subTodo.id === fromSubTodoId);
  const toIndex = savedTodos.value[parentIndex].children.findIndex(subTodo => subTodo.id === toSubTodoId);

  if (fromIndex === -1 || toIndex === -1) return;

  // 移动子待办项
  const [movedSubTodo] = savedTodos.value[parentIndex].children.splice(fromIndex, 1);
  savedTodos.value[parentIndex].children.splice(toIndex, 0, movedSubTodo);

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('子待办已移动', 'success');
};

// 将子待办项移动到另一个父待办项下
const moveSubTodoToAnotherParent = (fromParentId: string, subTodoId: string, toParentId: string, toSubTodoId?: string) => {
  const fromParentIndex = savedTodos.value.findIndex(todo => todo.id === fromParentId);
  const toParentIndex = savedTodos.value.findIndex(todo => todo.id === toParentId);

  if (fromParentIndex === -1 || toParentIndex === -1 || !savedTodos.value[fromParentIndex].children) return;

  const subTodoIndex = savedTodos.value[fromParentIndex].children.findIndex(subTodo => subTodo.id === subTodoId);
  if (subTodoIndex === -1) return;

  // 确保目标父待办项有children数组
  if (!savedTodos.value[toParentIndex].children) {
    savedTodos.value[toParentIndex].children = [];
  }

  // 从原父待办项中移除子待办项
  const [movedSubTodo] = savedTodos.value[fromParentIndex].children.splice(subTodoIndex, 1);

  // 更新子待办项的parentId
  movedSubTodo.parentId = toParentId;

  // 如果指定了目标子待办项，则插入到目标子待办项之前
  if (toSubTodoId) {
    const toSubTodoIndex = savedTodos.value[toParentIndex].children.findIndex(subTodo => subTodo.id === toSubTodoId);
    if (toSubTodoIndex !== -1) {
      savedTodos.value[toParentIndex].children.splice(toSubTodoIndex, 0, movedSubTodo);
    } else {
      savedTodos.value[toParentIndex].children.push(movedSubTodo);
    }
  } else {
    // 否则添加到末尾
    savedTodos.value[toParentIndex].children.push(movedSubTodo);
  }

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('子待办已移动到新的父待办下', 'success');
};

// 将顶级待办项转换为子待办项
const convertTodoToSubTodo = (todoId: string, parentId: string, targetSubTodoId?: string) => {
  const todoIndex = savedTodos.value.findIndex(todo => todo.id === todoId);
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);

  if (todoIndex === -1 || parentIndex === -1) return;

  // 从顶级待办项中移除
  const [movedTodo] = savedTodos.value.splice(todoIndex, 1);

  // 创建新的子待办项
  const newSubTodo = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
    title: movedTodo.title,
    completed: false,
    date: movedTodo.date,
    parentId: parentId,
    // 如果原待办项有子项，保留它们
    children: movedTodo.children
  };

  // 确保父待办项有children数组
  if (!savedTodos.value[parentIndex].children) {
    savedTodos.value[parentIndex].children = [];
  }

  // 如果指定了目标子待办项，则插入到目标子待办项之前
  if (targetSubTodoId) {
    const targetIndex = savedTodos.value[parentIndex].children.findIndex(subTodo => subTodo.id === targetSubTodoId);
    if (targetIndex !== -1) {
      savedTodos.value[parentIndex].children.splice(targetIndex, 0, newSubTodo);
    } else {
      savedTodos.value[parentIndex].children.push(newSubTodo);
    }
  } else {
    // 否则添加到末尾
    savedTodos.value[parentIndex].children.push(newSubTodo);
  }

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification('待办已转换为子待办', 'success');
};

// 切换子项批量操作模式
const toggleSubTodoBatchMode = (parentId: string) => {
  isSubTodoBatchMode.value = !isSubTodoBatchMode.value;

  // 清空已选择的子项
  selectedSubTodos.value.clear();

  if (isSubTodoBatchMode.value) {
    // 初始化当前父待办的选择集合
    selectedSubTodos.value.set(parentId, new Set());
    showNotification('已进入子项批量操作模式', 'info');
  } else {
    showNotification('已退出子项批量操作模式', 'info');
  }
};

// 切换子项选中状态
const toggleSubTodoSelection = (parentId: string, subTodoId: string, event: Event) => {
  event.stopPropagation();

  if (!isSubTodoBatchMode.value) return;

  // 确保父待办ID的选择集合存在
  if (!selectedSubTodos.value.has(parentId)) {
    selectedSubTodos.value.set(parentId, new Set());
  }

  const selections = selectedSubTodos.value.get(parentId)!;

  if (selections.has(subTodoId)) {
    selections.delete(subTodoId);
  } else {
    selections.add(subTodoId);
  }
};

// 批量完成选中的子项
const completeSelectedSubTodos = (parentId: string) => {
  if (!isSubTodoBatchMode.value || !selectedSubTodos.value.has(parentId)) return;

  const selections = selectedSubTodos.value.get(parentId)!;
  if (selections.size === 0) {
    showNotification('请至少选择一个子项', 'error');
    return;
  }

  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1 || !savedTodos.value[parentIndex].children) return;

  // 将选中的子项标记为已完成
  savedTodos.value[parentIndex].children.forEach(subTodo => {
    if (selections.has(subTodo.id)) {
      subTodo.completed = true;
    }
  });

  utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
  showNotification(`已完成 ${selections.size} 个子项`, 'success');

  // 清空选择
  selections.clear();
};

// 批量删除选中的子项
const deleteSelectedSubTodos = (parentId: string) => {
  if (!isSubTodoBatchMode.value || !selectedSubTodos.value.has(parentId)) return;

  const selections = selectedSubTodos.value.get(parentId)!;
  if (selections.size === 0) {
    showNotification('请至少选择一个子项', 'error');
    return;
  }

  // 显示确认对话框
  showConfirmDialog(
    `确定要删除选中的 ${selections.size} 个子项吗？`,
    () => {
      // 找到父待办
      const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
      if (parentIndex === -1 || !savedTodos.value[parentIndex].children) {
        closeConfirmDialog();
        return;
      }

      // 从后往前删除选中的子项，避免索引变化
      const subTodos = savedTodos.value[parentIndex].children;
      for (let i = subTodos.length - 1; i >= 0; i--) {
        if (selections.has(subTodos[i].id)) {
          subTodos.splice(i, 1);
        }
      }

      utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
      showNotification(`已删除 ${selections.size} 个子项`, 'info');

      // 清空选择
      selections.clear();
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 全选/取消全选子项
const toggleSelectAllSubTodos = (parentId: string) => {
  if (!isSubTodoBatchMode.value) return;

  // 找到父待办
  const parentIndex = savedTodos.value.findIndex(todo => todo.id === parentId);
  if (parentIndex === -1 || !savedTodos.value[parentIndex].children) return;

  // 确保父待办ID的选择集合存在
  if (!selectedSubTodos.value.has(parentId)) {
    selectedSubTodos.value.set(parentId, new Set());
  }

  const selections = selectedSubTodos.value.get(parentId)!;
  const subTodos = savedTodos.value[parentIndex].children;

  // 如果当前已全选，则取消全选
  if (selections.size === subTodos.length) {
    selections.clear();
    showNotification('已取消全选', 'info');
  } else {
    // 否则全选
    selections.clear();
    subTodos.forEach(subTodo => {
      selections.add(subTodo.id);
    });
    showNotification(`已全选 ${subTodos.length} 个子项`, 'info');
  }
};

// 切换紧凑视图模式
const toggleCompactMode = () => {
  isCompactMode.value = !isCompactMode.value;
  utools.dbStorage.setItem('compactMode', isCompactMode.value.toString());
  showNotification(isCompactMode.value ? '已启用紧凑视图' : '已禁用紧凑视图', 'info');
};

// 关闭子待办编辑器
const closeNoteEditor = () => {
  // 直接检查标题或内容变化
  const originalSubTodo = findSubTodoById(editingNote.value.id);
  if (originalSubTodo && (
    editingNote.value.title !== originalSubTodo.title ||
    editingNote.value.content !== (originalSubTodo as any).content)) {
    showConfirmDialog(
      '是否放弃未保存的更改？',
      () => {
        isEditing.value = false;
        closeConfirmDialog();
      },
      () => {
        closeConfirmDialog();
      }
    );
  } else {
    isEditing.value = false;
  }
};

// 查找子待办项的辅助函数
const findSubTodoById = (id: string) => {
  for (const todo of savedTodos.value) {
    if (todo.children) {
      const subTodo = todo.children.find(child => child.id === id);
      if (subTodo) return subTodo;
    }
  }
  return null;
};

// 保存子待办编辑内容
const saveNoteAndClose = () => {
  // 验证数据
  if (!editingNote.value.title.trim()) {
    showNotification('标题不能为空', 'error');
    return;
  }

  // 遍历所有待办查找与编辑中ID匹配的子待办
  let found = false;
  for (let i = 0; i < savedTodos.value.length; i++) {
    const todo = savedTodos.value[i];
    if (todo.children && todo.children.length > 0) {
      for (let j = 0; j < todo.children.length; j++) {
        const subTodo = todo.children[j];
        if (subTodo.id === editingNote.value.id) {
          // 统一格式：始终使用字符串格式的title，将content作为单独属性
          subTodo.title = editingNote.value.title.trim();
          (subTodo as any).content = editingNote.value.content || '';

          // 清理可能存在的嵌套title对象
          if (typeof subTodo.title === 'object') {
            subTodo.title = (subTodo.title as any).title || '';
          }

          // 保存更改
          utools.dbStorage.setItem('todos', JSON.stringify(savedTodos.value));
          showNotification('子待办已更新', 'success');
          found = true;
          break;
        }
      }
      if (found) break;
    }
  }

  if (!found) {
    showNotification('找不到要编辑的子待办项', 'error');
  }

  isEditing.value = false;
};

// Quill editor options
const editorOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link', 'image'],
      ['clean']
    ]
  },
  placeholder: '开始输入内容...'
};

// 编辑器搜索功能
// 显示临时通知
const showEditorNotification = (message: string) => {
  const editorView = document.querySelector('.note-editor-page');
  if (!editorView) return;

  const notification = document.createElement('div');
  notification.className = 'search-notification';
  notification.textContent = message;
  notification.style.position = 'absolute';
  notification.style.top = '60px';
  notification.style.left = '50%';
  notification.style.transform = 'translateX(-50%)';
  notification.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  notification.style.color = 'white';
  notification.style.padding = '8px 16px';
  notification.style.borderRadius = '4px';
  notification.style.zIndex = '1000';
  notification.style.opacity = '0';
  notification.style.transition = 'opacity 0.3s';

  editorView.appendChild(notification);

  setTimeout(() => {
    notification.style.opacity = '1';

    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 2000);
  }, 10);
};

// 获取Quill实例
const getQuillInstance = () => {
  if (!todoQuillEditorRef.value) return null;
  return todoQuillEditorRef.value.getQuill();
};

// 查找所有匹配项 - 改进版本，支持富文本内容
const findAllMatches = () => {
  const quill = getQuillInstance();
  if (!quill || !editorSearchQuery.value.trim()) {
    searchMatches.value = [];
    currentMatchIndex.value = -1;
    return;
  }

  const query = editorSearchQuery.value.trim().toLowerCase();
  const matches: number[] = [];

  try {
    // 获取编辑器的完整内容（Delta格式）
    const delta = quill.getContents();
    let textIndex = 0;
    let searchText = '';
    const indexMap: number[] = []; // 映射纯文本位置到Quill位置

    // 遍历Delta操作，构建搜索文本和位置映射
    delta.ops?.forEach((op: any) => {
      if (typeof op.insert === 'string') {
        // 文本内容
        const text = op.insert;
        for (let i = 0; i < text.length; i++) {
          searchText += text[i].toLowerCase();
          indexMap.push(textIndex + i);
        }
        textIndex += text.length;
      } else if (op.insert && typeof op.insert === 'object') {
        // 非文本内容（如图片、视频等），在搜索文本中跳过
        // 但在位置映射中占据一个位置
        textIndex += 1;
      }
    });

    // 在构建的搜索文本中查找匹配项
    let index = searchText.indexOf(query);
    let searchCount = 0;
    const maxSearches = 100;

    while (index !== -1 && searchCount < maxSearches) {
      // 将搜索文本中的位置映射回Quill位置
      if (index < indexMap.length) {
        matches.push(indexMap[index]);
      }
      index = searchText.indexOf(query, index + 1);
      searchCount++;
    }

    searchMatches.value = matches;
    currentMatchIndex.value = matches.length > 0 ? 0 : -1;

    if (matches.length > 0) {
      // 高亮第一个匹配项
      highlightMatch(matches[0], query.length);
    }
  } catch (error) {
    console.error('搜索过程中发生错误:', error);
    // 降级到简单的文本搜索
    const text = quill.getText();
    const lowerText = text.toLowerCase();
    let index = lowerText.indexOf(query);
    let searchCount = 0;
    const maxSearches = 100;

    while (index !== -1 && searchCount < maxSearches) {
      matches.push(index);
      index = lowerText.indexOf(query, index + 1);
      searchCount++;
    }

    searchMatches.value = matches;
    currentMatchIndex.value = matches.length > 0 ? 0 : -1;

    if (matches.length > 0) {
      highlightMatch(matches[0], query.length);
    }
  }
};

// 高亮并滚动到匹配项 - 修复滚动问题
const highlightMatch = (index: number, length: number) => {
  const quill = getQuillInstance();
  if (!quill) return;

  try {
    // 设置文本选择，这是最自然的"高亮"方式
    quill.setSelection(index, length, 'user');

    // 使用 setTimeout 确保选择渲染完成后再执行滚动
    setTimeout(() => {
      try {
        // 使用 Quill 的 API 获取光标所在位置的 DOM 节点
        const [leaf] = quill.getLeaf(index);

        if (leaf && leaf.domNode) {
          // leaf.domNode 可能是文本节点，需要获取其父元素
          const elementToScroll = leaf.domNode.nodeType === 3 ? leaf.domNode.parentNode : leaf.domNode;

          if (elementToScroll && typeof (elementToScroll as HTMLElement).scrollIntoView === 'function') {
            // 使用标准 scrollIntoView API，这是最可靠的滚动方法
            (elementToScroll as HTMLElement).scrollIntoView({
              behavior: 'smooth', // 平滑滚动，视觉效果更明显
              block: 'center',    // 将元素滚动到垂直方向的中心
              inline: 'nearest'   // 水平方向上就近对齐
            });
          } else {
            throw new Error('无法找到可滚动的有效 DOM 节点。');
          }
        } else {
          // 如果 getLeaf 失败，提供一个备用方案
          console.warn('Quill getLeaf API 未返回有效节点，尝试备用滚动方法。');
          const editorContainer = document.querySelector('.note-editor-page .ql-editor') as HTMLElement;
          const bounds = quill.getBounds(index);
          if (bounds && editorContainer) {
              const newScrollTop = bounds.top - (editorContainer.clientHeight / 2) + (bounds.height / 2);
              editorContainer.scrollTop = Math.max(0, newScrollTop);
          } else {
            throw new Error('备用滚动方法也失败了。');
          }
        }
      } catch (e) {
        console.error('滚动到搜索匹配项时发生错误:', e);
      }
    }, 100); // 100毫秒的延迟足以应对大多数渲染延迟
  } catch (e) {
    console.error('设置选择时发生错误:', e);
  }
};

const searchForward = () => {
  if (searchMatches.value.length === 0) {
    findAllMatches();
    return;
  }

  if (currentMatchIndex.value < searchMatches.value.length - 1) {
    currentMatchIndex.value++;
  } else {
    currentMatchIndex.value = 0;
    showEditorNotification('已循环到第一个搜索结果');
  }

  const index = searchMatches.value[currentMatchIndex.value];
  highlightMatch(index, editorSearchQuery.value.trim().length);
};

const searchBackward = () => {
  if (searchMatches.value.length === 0) {
    findAllMatches();
    return;
  }

  if (currentMatchIndex.value > 0) {
    currentMatchIndex.value--;
  } else {
    currentMatchIndex.value = searchMatches.value.length - 1;
    showEditorNotification('已循环到最后一个搜索结果');
  }

  const index = searchMatches.value[currentMatchIndex.value];
  highlightMatch(index, editorSearchQuery.value.trim().length);
};

const toggleEditorSearch = () => {
  showEditorSearch.value = !showEditorSearch.value;
  if (!showEditorSearch.value) {
    editorSearchQuery.value = '';
    searchMatches.value = [];
    currentMatchIndex.value = -1;
    const quill = getQuillInstance();
    if (quill) quill.setSelection(null);
  } else {
    nextTick(() => {
      const searchInput = document.querySelector('.note-editor-page .search-input') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    });
  }
};

const performSearch = () => {
  if (!editorSearchQuery.value.trim()) return;
  findAllMatches();
  if (searchMatches.value.length > 0) {
    currentMatchIndex.value = 0;
    const index = searchMatches.value[0];
    highlightMatch(index, editorSearchQuery.value.trim().length);
  }
};

// 处理编辑器搜索框鼠标离开事件
const handleEditorSearchMouseLeave = () => {
  if (editorSearchQuery.value.trim()) {
    performSearch();
  }
};

// 监听搜索关键词变化 - 只清除之前的搜索结果，不自动搜索
watch(editorSearchQuery, (newValue) => {
  // 只清除搜索结果，不操作Quill编辑器以避免焦点丢失
  searchMatches.value = [];
  currentMatchIndex.value = -1;
});
</script>

<template>
  <div class="notebook">
    <!-- 通知组件 -->
    <Notification :show="notification.show" :message="notification.message" :type="notification.type"
      @close="notification.show = false" />

    <!-- 确认对话框组件 -->
    <ConfirmDialog :show="confirmDialog.show" :message="confirmDialog.message" @confirm="confirmDialog.onConfirm"
      @cancel="confirmDialog.onCancel" />

    <!-- 编辑器视图 -->
    <Editor v-if="showEditor" :id="editingTodoId" :initial-title="title" :initial-content="todos" :initial-items="editingTodoId ? (savedTodos.find(t => t.id === editingTodoId)?.children?.map(child => ({
      id: child.id,
      title: child.title,
      content: (child as any).content || '',
      completed: child.completed,
      date: child.date,
      parentId: child.parentId
    })) || []) : []" content-placeholder="输入待办内容..." type="todo" @save="saveTodo" @cancel="cancelEdit" @showConfirm="(data) => {
      showConfirmDialog(
        data.message,
        () => {
          data.onConfirm();
          closeConfirmDialog();
        },
        () => {
          closeConfirmDialog();
        }
      );
    }" />

    <!-- 待办列表视图 -->
    <div v-else class="notes-view">
      <AppHeader title="待办" :is-batch-mode="isBatchDeleteMode" :is-compact-mode="isCompactMode"
        @toggle-batch-mode="toggleBatchDelete" @delete-selected="confirmBatchDelete"
        @toggle-compact-mode="toggleCompactMode" @search="handleSearch" />

      <div class="todos-container">
        <div class="todos-list">
          <div v-for="(todo, index) in todosWithGroupInfo" :key="todo.id" class="todo-item"
            :class="{ 'drag-over': dragOverTodoId === todo.id, 'compact-mode': isCompactMode }" draggable="true"
            @dragstart="startDragTodo(todo.id, $event)" @dragover="dragOverTodo(todo.id, $event)"
            @drop="dropTodo(todo.id, $event)" @dragend="endDrag">
            <!-- 待办卡片 - 点击进入编辑模式 -->
            <NoteCard :title="todo.title" :content="todo.content" :date="todo.date" :is-pinned="todo.isPinned"
              :reminder-date="todo.reminderDate" :reminder-time="todo.reminderTime" :is-expired="todo.isExpired"
              :is-grouped="todo.isGrouped" :is-first-in-group="todo.isFirstInGroup"
              :is-last-in-group="todo.isLastInGroup"
              :is-selected="isBatchDeleteMode && selectedTodos.has(savedTodos.indexOf(todo))"
              :is-batch-mode="isBatchDeleteMode"
              :layout-mode="'todo'"
              @click="isBatchDeleteMode ? toggleTodoSelection(savedTodos.indexOf(todo), $event) : loadTodo(todo)"
              @context-menu="!isBatchDeleteMode && showTodoContextMenu($event, todo)"
              @toggle-selection="toggleTodoSelection(savedTodos.indexOf(todo), $event)" />

            <!-- 进度条 - 仅当有子待办时显示 -->
            <div v-if="todo.children && todo.children.length > 0" class="todo-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: `${getTodoProgress(todo)}%` }"></div>
              </div>
              <div class="progress-text">
                {{ getCompletedSubTodosCount(todo) }}/{{ getTotalSubTodosCount(todo) }} 已完成
              </div>
              <!-- 添加展开/收起按钮 -->
              <button class="expand-toggle" @click.stop="toggleExpandTodo(todo.id)">
                {{ expandedTodos.has(todo.id) ? '收起 ▲' : '展开 ▼' }}
              </button>
            </div>

            <!-- 子项列表 - 仅在展开状态时显示 -->
            <div v-if="todo.children && todo.children.length > 0 && expandedTodos.has(todo.id)" class="sub-todos"
              :class="{ 'compact-sub-todos': isCompactMode }">
              <div v-for="(subTodo, subIndex) in todo.children" :key="subTodo.id" class="sub-todo-item"
                :class="{ 'drag-over': dragOverTodoId === todo.id && dragOverSubTodoId === subTodo.id, 'compact-sub-item': isCompactMode }"
                draggable="true" @dragstart="startDragSubTodo(todo.id, subTodo.id, $event)"
                @dragover="dragOverSubTodo(todo.id, subTodo.id, $event)"
                @drop="dropSubTodo(todo.id, subTodo.id, $event)" @dragend="endDrag">
                <div class="sub-todo-content" @click="loadTodo(subTodo)">
                  <input type="checkbox" v-model="subTodo.completed" @change="saveSubTodoStatus(index, subIndex)"
                    @click.stop>
                  <span :class="{ completed: subTodo.completed }">{{
                    typeof subTodo.title === 'object' && subTodo.title !== null
                      ? (subTodo.title as any).title
                      : subTodo.title
                  }}</span>
                  <div class="sub-todo-actions">
                    <button class="delete-sub-todo" @click.stop="deleteSubTodo(index, subIndex)">删除</button>
                  </div>
                </div>

                <!-- 递归显示嵌套子项 -->
                <div v-if="subTodo.children && subTodo.children.length > 0" class="nested-todos"
                  :class="{ 'compact-nested-todos': isCompactMode }">
                  <div v-for="(nestedTodo, nestedIndex) in subTodo.children" :key="nestedTodo.id"
                    class="nested-todo-item" :class="{ 'compact-nested-item': isCompactMode }">
                    <div class="nested-todo-content" @click="loadTodo(nestedTodo)">
                      <input type="checkbox" v-model="nestedTodo.completed"
                        @change="saveNestedTodoStatus(index, subIndex, nestedIndex)">
                      <span :class="{ completed: nestedTodo.completed }">{{
                        typeof nestedTodo.title === 'object' && nestedTodo.title !== null
                          ? (nestedTodo.title as any).title
                          : nestedTodo.title
                      }}</span>
                      <button class="delete-nested-todo"
                        @click.stop="deleteNestedTodo(index, subIndex, nestedIndex)">删除</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>



      <!-- 右键菜单 -->
      <ContextMenu :show="showContextMenu" :position="contextMenuPosition"
        :is-pinned="selectedTodoIndex >= 0 ? savedTodos[selectedTodoIndex]?.isPinned : false"
        :type="isSubTodoMenu ? 'subtodo' : 'todo'" @toggle-pin="togglePinTodo()" @delete="deleteTodo()"
        @quick-add="handleSubTodoAction('quick-add')" @batch-add="handleSubTodoAction('batch-add')"
        @complete-all="handleSubTodoAction('complete-all')" @clear-all="handleSubTodoAction('clear-all')" />

      <AddButton @click="createNewTodo" />
    </div>
    <!-- 富文本编辑器全屏页面 -->
    <div v-if="isEditing" class="note-editor-page">
      <div class="editor-header">
        <div class="header-left">
          <button class="icon-btn cancel-icon" @click="closeNoteEditor" title="返回">
            <IconCancel class="icon w-5 h-5" />
          </button>
        </div>
        <div class="header-actions">
          <button class="icon-btn" @click="toggleEditorSearch" :class="{ active: showEditorSearch }" title="搜索">
            <IconSearch class="icon w-5 h-5" />
          </button>
          <button class="icon-btn save-icon" @click="saveNoteAndClose" title="保存">
            <IconSave class="icon w-5 h-5" />
          </button>
        </div>
      </div>

      <div class="search-bar" v-if="showEditorSearch" @mouseleave="handleEditorSearchMouseLeave">
        <input class="search-input" v-model="editorSearchQuery" placeholder="搜索..." @keyup.enter="searchForward"
          @keyup.esc="toggleEditorSearch" @keydown.up.prevent="searchBackward" @keydown.down.prevent="searchForward" />
        <div class="search-actions">
          <button class="nav-btn" @click="searchBackward" title="向上查找 (↑)">
            <span class="arrow up">↑</span>
          </button>
          <button class="nav-btn" @click="searchForward" title="向下查找 (↓)">
            <span class="arrow down">↓</span>
          </button>
          <button class="search-btn" @click="performSearch">查找</button>
          <span v-if="searchMatches.length > 0" class="match-count">
            {{ currentMatchIndex + 1 }}/{{ searchMatches.length }}
          </span>
          <button class="close-btn" @click="toggleEditorSearch" title="关闭 (ESC)">×</button>
        </div>
      </div>

      <div class="editor-content">
        <input v-model="editingNote.title" type="text" placeholder="标题" class="title-input" />
        <QuillEditor ref="todoQuillEditorRef" v-model:content="editingNote.content" :options="editorOptions"
          contentType="html" class="rich-editor" @textChange="(delta, oldDelta, source) => {
            console.log('QuillEditor 内容变更:', { delta, oldDelta, source, currentContent: editingNote.content });
          }" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.notebook {
  max-width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #f8f9fa;
}

.notes-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-bottom: 80px;
}

.todos-container {
  flex: 1;
  overflow-y: auto;
  padding: 3px 0;
}

.todos-list {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-hint {
  font-size: 14px;
  margin-top: 8px;
  color: #aaa;
}

.notes-grid {
  display: none;
  /* 不再使用网格布局 */
}



.todo-item {
  margin-bottom: 6px;
}

/* 紧凑模式下的待办项 */
.todo-item.compact-mode {
  margin-bottom: 3px;
}

.sub-todos {
  margin-left: 12px;
  margin-top: 4px;
  padding: 4px 0;
}

/* 紧凑模式下的子项列表 */
.sub-todos.compact-sub-todos {
  margin-left: 8px;
  margin-top: 2px;
  padding: 2px 0;
}

.sub-todo-item {
  padding: 6px 8px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.sub-todo-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 紧凑模式下的子项 */
.sub-todo-item.compact-sub-item {
  padding: 4px 6px;
  margin-bottom: 2px;
  border-radius: 3px;
}

.sub-todo-content {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.sub-todo-content input[type="checkbox"] {
  width: 14px;
  height: 14px;
  cursor: pointer;
  flex-shrink: 0;
}

.sub-todo-content span {
  flex: 1;
  font-size: 12px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
}

.sub-todo-actions {
  margin-left: auto;
  display: flex;
  gap: 4px;
}

.completed {
  text-decoration: line-through;
  color: #999;
  opacity: 0.7;
}

.add-sub-todo {
  margin-top: 8px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s ease;
}

.add-sub-todo:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #495057;
}

.delete-sub-todo {
  padding: 2px 6px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.delete-sub-todo:hover {
  background-color: #d32f2f;
}

/* 嵌套子项样式 */
.nested-todos {
  margin-left: 12px;
  margin-top: 2px;
}

/* 紧凑模式下的嵌套子项列表 */
.nested-todos.compact-nested-todos {
  margin-left: 8px;
  margin-top: 1px;
}

.nested-todo-item {
  padding: 2px 4px;
  background-color: #f9f9f9;
  border-radius: 2px;
  margin-bottom: 1px;
  border-left: 2px solid #ddd;
  font-size: 11px;
}

/* 紧凑模式下的嵌套子项 */
.nested-todo-item.compact-nested-item {
  padding: 1px 3px;
  margin-bottom: 1px;
  font-size: 10px;
}

.nested-todo-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.delete-nested-todo {
  padding: 1px 3px;
  background-color: #ff4444;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 9px;
}

.delete-nested-todo:hover {
  background-color: #cc0000;
}

/* 进度条样式 */
.todo-progress {
  display: flex;
  align-items: center;
  padding: 4px 8px 6px;
  font-size: 11px;
  color: #666;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  margin: 0 -8px;
  margin-top: 4px;
  gap: 8px;
}

.progress-bar {
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  min-width: 60px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 10px;
  font-weight: 500;
  color: #555;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 拖拽样式 */
.todo-item.drag-over {
  border: 1px dashed #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

.sub-todo-item.drag-over {
  border: 1px dashed #2196f3;
  background-color: rgba(33, 150, 243, 0.05);
}

/* 添加展开/收起按钮样式 */
.expand-toggle {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
  flex-shrink: 0;
}

.expand-toggle:hover {
  background-color: #1565c0;
}

/* 子待办展开/收起动画 */
.sub-todos {
  animation: slideDown 0.3s ease-out forwards;
  overflow: hidden;
  transform-origin: top;
}

@keyframes slideDown {
  0% {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  100% {
    max-height: 1000px;
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  0% {
    max-height: 1000px;
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 富文本编辑器页面样式 */
.note-editor-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.2s ease-out;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  border-bottom: 1px solid #eef2f6;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  min-height: 44px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 15px;
  color: #64748b;
  cursor: pointer;
  padding: 5px;
  border-radius: 6px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-btn:hover {
  background-color: #f1f5f9;
  color: #334155;
  transform: translateY(-1px);
}

.icon-btn.active {
  color: #2563eb;
  background-color: rgba(37, 99, 235, 0.08);
}



.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  padding-bottom: 50px;
  /* 增加底部内边距 */
  overflow-y: auto;
  max-height: calc(100vh - 100px);
  /* 限制最大高度，确保不会超出视口 */
}

.title-input {
  border: none;
  border-bottom: 1px solid #eee;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 12px;
  padding: 6px 0;
  outline: none;
}

.content-textarea {
  flex: 1;
  border: none;
  font-size: 15px;
  line-height: 1.6;
  padding: 6px 0;
  resize: none;
  outline: none;
  min-height: 200px;
}

/* 富文本编辑器样式 */
.rich-editor {
  height: auto;
  /* 改为auto，允许编辑器随内容增长 */
  min-height: 200px;
  /* 设置最小高度 */
  flex: 1;
  /* 使编辑器占满剩余空间 */
  font-size: 15px;
  line-height: 1.6;
  color: #334155;
  overflow-y: auto;
  /* 允许内容区域滚动 */
}

/* 确保 Quill 编辑器的容器能够正确显示 */
:deep(.ql-container) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 15px;
  min-height: 200px;
  /* 设置最小高度 */
  height: auto;
  /* 允许容器随内容增长 */
  max-height: calc(100vh - 200px);
  /* 确保不会超出视口 */
  overflow-y: auto;
  /* 允许容器滚动 */
}

:deep(.ql-toolbar) {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  background-color: #f8fafc;
  padding: 4px 6px !important;
}

:deep(.ql-formats) {
  margin-right: 8px !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}





/* 编辑器搜索框样式 */
.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background-color: #f0f7ff;
  border-bottom: 1px solid #d0e3ff;
  animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: sticky;
  top: 0;
  background: linear-gradient(to right, #f0f7ff, #e6f0ff);
}

.search-input {
  flex: 1;
  border: 1px solid #c0d8ff;
  border-radius: 6px;
  padding: 8px 14px;
  font-size: 15px;
  outline: none;
  background-color: white;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) inset;
  font-weight: 500;
}

.search-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 6px;
}

.nav-btn {
  background-color: #e9f0fd;
  border: 1px solid #c0d8ff;
  color: #3b82f6;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.nav-btn:hover {
  background-color: #dbe7fd;
  color: #2563eb;
  transform: translateY(-1px);
}

.search-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(37, 99, 235, 0.3);
}

.search-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #555;
  cursor: pointer;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 16px;
  font-weight: bold;
}

.match-count {
  font-size: 12px;
  color: #64748b;
  margin: 0 4px;
  min-width: 40px;
  text-align: center;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease-out;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
