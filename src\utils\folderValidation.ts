// 文件夹数据验证工具函数

import type { Folder, FolderValidationError, CreateFolderParams } from '../types/folder.js';

/**
 * 验证文件夹名称是否有效
 */
export function validateFolderName(name: string): { isValid: boolean; error?: FolderValidationError } {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'EMPTY_NAME' as FolderValidationError };
  }
  
  // 检查名称长度
  if (name.trim().length > 50) {
    return { isValid: false, error: 'INVALID_NAME' as FolderValidationError };
  }
  
  return { isValid: true };
}

/**
 * 检查文件夹名称是否重复
 */
export function checkDuplicateFolderName(
  name: string, 
  parentId: string | null, 
  folders: Folder[], 
  excludeId?: string
): boolean {
  return folders.some(folder => 
    folder.name.toLowerCase() === name.toLowerCase() && 
    folder.parentId === parentId &&
    folder.id !== excludeId
  );
}

/**
 * 验证父文件夹是否有效
 */
export function validateParentFolder(parentId: string | null, folders: Folder[]): boolean {
  if (parentId === null) return true;
  return folders.some(folder => folder.id === parentId);
}

/**
 * 检查是否会形成循环引用
 */
export function checkCircularReference(folderId: string, parentId: string | null, folders: Folder[]): boolean {
  if (parentId === null) return false;
  if (parentId === folderId) return true;
  
  const parent = folders.find(f => f.id === parentId);
  if (!parent) return false;
  
  return checkCircularReference(folderId, parent.parentId, folders);
}

/**
 * 验证创建文件夹的参数
 */
export function validateCreateFolder(
  params: CreateFolderParams, 
  folders: Folder[]
): { isValid: boolean; error?: string } {
  // 验证名称
  const nameValidation = validateFolderName(params.name);
  if (!nameValidation.isValid) {
    return { 
      isValid: false, 
      error: nameValidation.error === 'EMPTY_NAME' ? '文件夹名称不能为空' : '文件夹名称无效' 
    };
  }
  
  // 检查重名
  if (checkDuplicateFolderName(params.name, params.parentId || null, folders)) {
    return { isValid: false, error: '文件夹名称已存在' };
  }
  
  // 验证父文件夹
  if (params.parentId && !validateParentFolder(params.parentId, folders)) {
    return { isValid: false, error: '父文件夹不存在' };
  }
  
  return { isValid: true };
}

/**
 * 验证更新文件夹的参数
 */
export function validateUpdateFolder(
  folderId: string,
  newName: string,
  folders: Folder[]
): { isValid: boolean; error?: string } {
  const folder = folders.find(f => f.id === folderId);
  if (!folder) {
    return { isValid: false, error: '文件夹不存在' };
  }
  
  // 验证名称
  const nameValidation = validateFolderName(newName);
  if (!nameValidation.isValid) {
    return { 
      isValid: false, 
      error: nameValidation.error === 'EMPTY_NAME' ? '文件夹名称不能为空' : '文件夹名称无效' 
    };
  }
  
  // 检查重名（排除自己）
  if (checkDuplicateFolderName(newName, folder.parentId, folders, folderId)) {
    return { isValid: false, error: '文件夹名称已存在' };
  }
  
  return { isValid: true };
}