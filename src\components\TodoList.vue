<script lang="ts" setup>
import { ref } from 'vue';
import TodoItem from './TodoItem.vue';

interface Todo {
    id: number;
    content: string;
    completed: boolean;
    children?: Todo[];
}

const todos = ref<Todo[]>([]);
const newTodo = ref('');
const showAddChildDialog = ref(false);
const selectedParentId = ref<number | null>(null);
const childItems = ref<string[]>(['']); // 用于存储多个子项输入

const addTodo = () => {
    todos.value.push({
        id: Date.now(),
        content: '新待办项',
        completed: false,
        children: []
    });
};

const addChildInput = () => {
    childItems.value.push('');
};

const removeChildInput = (index: number) => {
    childItems.value.splice(index, 1);
};

const addChild = (parentId: number) => {
    selectedParentId.value = parentId;
    childItems.value = [''];
    showAddChildDialog.value = true;
};

const confirmAddChildren = () => {
    if (!selectedParentId.value) return;

    const validItems = childItems.value.filter(item => item.trim());
    if (validItems.length === 0) return;

    const addChildrenToItem = (items: Todo[]) => {
        for (const item of items) {
            if (item.id === selectedParentId.value) {
                item.children = item.children || [];
                validItems.forEach(content => {
                    item.children!.push({
                        id: Date.now() + Math.random(),
                        content,
                        completed: false,
                        children: []
                    });
                });
                return true;
            }
            if (item.children && addChildrenToItem(item.children)) {
                return true;
            }
        }
        return false;
    };

    addChildrenToItem(todos.value);
    showAddChildDialog.value = false;
    childItems.value = [''];
    selectedParentId.value = null;
};

const toggleTodo = (id: number) => {
    const toggleItem = (items: Todo[]) => {
        for (const item of items) {
            if (item.id === id) {
                item.completed = !item.completed;
                return;
            }
            if (item.children) {
                toggleItem(item.children);
            }
        }
    };

    toggleItem(todos.value);
};

const deleteTodo = (id: number) => {
    const deleteItem = (items: Todo[]) => {
        const index = items.findIndex(item => item.id === id);
        if (index !== -1) {
            items.splice(index, 1);
            return true;
        }
        for (const item of items) {
            if (item.children && deleteItem(item.children)) {
                return true;
            }
        }
        return false;
    };

    deleteItem(todos.value);
};
</script>

<template>
    <div class="todo-list">
        <div class="todo-input">
            <button @click="addTodo">添加待办项</button>
        </div>
        <div class="todo-items">
            <TodoItem v-for="todo in todos" :key="todo.id" :item="todo" :level="0" @toggle="toggleTodo"
                @add-child="addChild" @delete="deleteTodo" />
        </div>

        <!-- 添加子项对话框 -->
        <div v-if="showAddChildDialog" class="dialog-overlay">
            <div class="dialog">
                <h3>添加子待办项</h3>
                <div v-for="(item, index) in childItems" :key="index" class="child-input">
                    <input v-model="childItems[index]" placeholder="输入子待办项内容" />
                    <button v-if="index === childItems.length - 1" @click="addChildInput">+</button>
                    <button v-if="childItems.length > 1" @click="removeChildInput(index)">-</button>
                </div>
                <div class="dialog-actions">
                    <button @click="confirmAddChildren">确定</button>
                    <button @click="showAddChildDialog = false">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.todo-list {
    padding: 16px;
}

.todo-input {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.todo-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.todo-input button {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.todo-input button:hover {
    background-color: #45a049;
}

.todo-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.dialog {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    min-width: 300px;
}

.child-input {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.child-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

.dialog-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.dialog-actions button:first-child {
    background-color: #4CAF50;
    color: white;
}

.dialog-actions button:last-child {
    background-color: #f0f0f0;
}
</style>