<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索焦点测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .search-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 14px;
            background-color: #f0f7ff;
            border-bottom: 1px solid #d0e3ff;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .search-input {
            flex: 1;
            border: 1px solid #c0d8ff;
            border-radius: 6px;
            padding: 8px 14px;
            font-size: 15px;
            outline: none;
            background-color: white;
            transition: all 0.2s ease;
        }
        
        .search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }
        
        .search-actions {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-left: 6px;
        }
        
        .search-btn {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .search-btn:hover {
            background-color: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.focus {
            color: #28a745;
        }
        
        .log-entry.blur {
            color: #dc3545;
        }
        
        .log-entry.input {
            color: #007bff;
        }
        
        .log-entry.search {
            color: #6f42c1;
            font-weight: bold;
        }
        
        .test-info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .test-steps {
            margin-top: 10px;
        }
        
        .test-steps ol {
            margin-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本搜索焦点修复测试</h1>
        
        <div class="test-info">
            <h3>测试步骤：</h3>
            <div class="test-steps">
                <ol>
                    <li>在搜索框中连续输入多个字符（如："测试文字"）</li>
                    <li>观察输入框是否保持焦点（不会在输入过程中失去焦点）</li>
                    <li>鼠标移出搜索框区域时，应该触发搜索</li>
                    <li>点击"查找"按钮也应该触发搜索</li>
                    <li>查看下方日志记录焦点变化</li>
                </ol>
            </div>
        </div>
        
        <div class="search-bar" id="searchBar">
            <input 
                class="search-input" 
                id="searchInput" 
                placeholder="输入搜索内容..." 
                autocomplete="off"
            />
            <div class="search-actions">
                <button class="search-btn" id="searchBtn">查找</button>
            </div>
        </div>
        
        <div class="log" id="log">
            <div class="log-entry">日志记录开始...</div>
        </div>
    </div>
    
    <script>
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const searchBar = document.getElementById('searchBar');
        const log = document.getElementById('log');
        
        let inputValue = '';
        
        function addLog(message, type = '') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function performSearch() {
            const query = searchInput.value.trim();
            if (query) {
                addLog(`执行搜索: "${query}"`, 'search');
            } else {
                addLog('搜索内容为空，跳过搜索', 'search');
            }
        }
        
        // 监听输入事件
        searchInput.addEventListener('input', (e) => {
            inputValue = e.target.value;
            addLog(`输入内容: "${inputValue}" (焦点状态: ${document.activeElement === searchInput ? '有焦点' : '无焦点'})`, 'input');
        });
        
        // 监听焦点获得
        searchInput.addEventListener('focus', () => {
            addLog('搜索框获得焦点', 'focus');
        });
        
        // 监听焦点失去
        searchInput.addEventListener('blur', () => {
            addLog('搜索框失去焦点', 'blur');
        });
        
        // 鼠标离开搜索区域时触发搜索
        searchBar.addEventListener('mouseleave', () => {
            if (searchInput.value.trim()) {
                addLog('鼠标离开搜索区域，触发搜索', 'search');
                performSearch();
            }
        });
        
        // 点击搜索按钮
        searchBtn.addEventListener('click', () => {
            addLog('点击搜索按钮', 'search');
            performSearch();
        });
        
        // 回车键搜索
        searchInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') {
                addLog('按下回车键，触发搜索', 'search');
                performSearch();
            }
        });
        
        // 初始化
        addLog('测试页面加载完成');
        addLog('请在搜索框中连续输入文字测试焦点保持情况');
    </script>
</body>
</html>
