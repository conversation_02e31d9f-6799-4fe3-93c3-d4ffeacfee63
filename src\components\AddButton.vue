<script lang="ts" setup>
const emit = defineEmits(['click']);

// 点击添加按钮
const handleClick = () => {
  emit('click');
};
</script>

<template>
  <button @click="handleClick" class="add-btn">
    <span>+</span>
  </button>
</template>

<style scoped>
.add-btn {
  position: fixed;
  right: 24px;
  bottom: 14px;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  color: white;
  border: none;
  font-size: 18px;
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 8px rgba(25, 118, 210, 0.3);
  cursor: pointer;
  z-index: 101;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
}

.add-btn:active {
  transform: scale(0.98) translateY(0px);
  box-shadow: 0 2px 6px rgba(25, 118, 210, 0.4);
}
</style>