<script lang="ts" setup>
interface TodoItem {
  id: number;
  content: string;
  completed: boolean;
  children?: TodoItem[];
}

defineProps({
  item: {
    type: Object as () => TodoItem,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['toggle', 'add-child', 'delete']);

const toggleComplete = () => {
  emit('toggle', props.item.id);
};

const addChild = () => {
  emit('add-child', props.item.id);
};

const deleteTodo = () => {
  emit('delete', props.item.id);
};
</script>

<template>
  <div class="todo-item" :style="{ paddingLeft: `${level * 20}px` }">
    <div class="todo-content">
      <input type="checkbox" :checked="item.completed" @change="toggleComplete">
      <span :class="{ completed: item.completed }">{{ item.content }}</span>
      <div class="todo-actions">
        <button @click="addChild">添加子项</button>
        <button @click="deleteTodo">删除</button>
      </div>
    </div>
    <div v-if="item.children && item.children.length" class="todo-children">
      <TodoItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        @toggle="$emit('toggle', $event)"
        @add-child="$emit('add-child', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<style scoped>
.todo-item {
  margin: 8px 0;
}

.todo-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.completed {
  text-decoration: line-through;
  color: #999;
}

.todo-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.todo-actions button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
}

.todo-actions button:hover {
  background-color: #e0e0e0;
}

.todo-children {
  margin-left: 20px;
}
</style>