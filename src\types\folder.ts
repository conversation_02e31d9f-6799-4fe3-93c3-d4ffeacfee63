// 文件夹相关的类型定义

export interface Folder {
  id: string;
  name: string;
  parentId: string | null; // null 表示根文件夹
  createdDate: string;
  updatedDate: string;
  color?: string; // 可选的文件夹颜色
  icon?: string; // 可选的文件夹图标
}

export interface FolderTreeNode {
  folder: Folder;
  children: FolderTreeNode[];
  noteCount: number;
}

// 扩展的笔记接口
export interface NoteWithFolder {
  id: string;
  title: string;
  content: string;
  date: string;
  isPinned?: boolean;
  folderId?: string | null; // 新增：所属文件夹ID，null表示根目录
  reminderDate?: string;
  reminderTime?: string;
}

// 文件夹统计信息
export interface FolderStats {
  noteCount: number;
  subFolderCount: number;
  totalNoteCount: number; // 包含子文件夹的笔记总数
}

// 文件夹操作结果
export interface FolderOperationResult {
  success: boolean;
  message: string;
  data?: any;
}

// 文件夹验证错误类型
export enum FolderValidationError {
  EMPTY_NAME = 'EMPTY_NAME',
  DUPLICATE_NAME = 'DUPLICATE_NAME',
  INVALID_PARENT = 'INVALID_PARENT',
  CIRCULAR_REFERENCE = 'CIRCULAR_REFERENCE'
}

// 文件夹创建参数
export interface CreateFolderParams {
  name: string;
  parentId?: string | null;
  color?: string;
  icon?: string;
}

// 文件夹更新参数
export interface UpdateFolderParams {
  id: string;
  name?: string;
  color?: string;
  icon?: string;
}

// 移动笔记参数
export interface MoveNoteParams {
  noteId: string;
  targetFolderId: string | null;
}