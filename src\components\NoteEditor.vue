<script lang="ts" setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import {
  MagnifyingGlassIcon as IconSearch,
  BellIcon as IconBell,
  XMarkIcon as IconCancel,
  CheckIcon as IconSave
} from '@heroicons/vue/24/outline';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

let quillModulesRegistered = false;

const props = defineProps<{
  id?: string;
  initialTitle?: string;
  initialContent?: string;
  placeholder?: string;
}>();

const emit = defineEmits(['save', 'cancel', 'showConfirm']);

const title = ref(props.initialTitle || '');
const content = ref(props.initialContent || '');

console.log('组件初始化时设置内容:', {
  initialTitle: props.initialTitle,
  initialContent: props.initialContent,
  title: title.value,
  content: content.value
});

const reminderDate = ref('');
const reminderTime = ref('');
const showReminder = ref(false);

// Track original values for change detection
const originalTitle = ref(props.initialTitle || '');
const originalContent = ref(props.initialContent || '');

// Quill editor options
const editorOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link', 'image'],
      ['clean']
    ]
  },
  placeholder: props.placeholder || '开始输入内容...'
};

const onEditorReady = (quill: any) => {
  // This is the safe way to interact with the Quill instance
  quill.root.addEventListener('click', (e: MouseEvent) => {
    let target = e.target as HTMLElement | null;
    if (target && target.tagName !== 'A') {
      target = target.closest('a');
    }
    
    if (target && target.tagName === 'A') {
      let href = target.getAttribute('href');
      if (href) {
        e.preventDefault();

        // Ensure the URL has a protocol
        if (!/^https/i.test(href) && !/^http/i.test(href)) {
          href = 'https://' + href;
        }

        // Use the utools API to open links externally
        if (typeof window !== 'undefined' && window.utools) {
          window.utools.shellOpenExternal(href);
        } else {
          // Fallback for non-utools environments
          window.open(href, '_blank');
        }
      }
    }
  }, true); // Use capture phase for reliability
};

onMounted(() => {
  // All previous logic removed from onMounted to prevent race conditions
  originalTitle.value = props.initialTitle || '';
  originalContent.value = props.initialContent || '';

  // 确保初始内容正确设置
  content.value = props.initialContent || '';
  console.log('初始内容设置:', content.value);
});

// 监听内容变化
watch(content, (newContent) => {
  console.log('内容已更新:', newContent);
}, { deep: true });

// Change detection
const isContentChanged = computed(() => {
  const currentTitle = title.value.trim();
  const currentContent = content.value.trim();
  const originalTitleTrimmed = originalTitle.value.trim();
  const originalContentTrimmed = originalContent.value.trim();
  return currentTitle !== originalTitleTrimmed || currentContent !== originalContentTrimmed;
});

// 搜索功能需要重新实现，因为富文本编辑器的搜索机制不同
const searchQuery = ref('');
const showSearch = ref(false);
const quillEditorRef = ref(null);
const searchMatches = ref<number[]>([]);
const currentMatchIndex = ref(-1);

// 获取Quill实例
const getQuillInstance = () => {
  if (!quillEditorRef.value) return null;
  // 使用any类型解决类型错误
  return (quillEditorRef.value as any).getQuill();
};

// 查找所有匹配项 - 改进版本，支持富文本内容
const findAllMatches = () => {
  const quill = getQuillInstance();
  if (!quill || !searchQuery.value.trim()) {
    searchMatches.value = [];
    currentMatchIndex.value = -1;
    return;
  }

  const query = searchQuery.value.trim().toLowerCase();
  const matches: number[] = [];

  try {
    // 获取编辑器的完整内容（Delta格式）
    const delta = quill.getContents();
    let textIndex = 0;
    let searchText = '';
    const indexMap: number[] = []; // 映射纯文本位置到Quill位置

    // 遍历Delta操作，构建搜索文本和位置映射
    delta.ops?.forEach((op: any) => {
      if (typeof op.insert === 'string') {
        // 文本内容
        const text = op.insert;
        for (let i = 0; i < text.length; i++) {
          searchText += text[i].toLowerCase();
          indexMap.push(textIndex + i);
        }
        textIndex += text.length;
      } else if (op.insert && typeof op.insert === 'object') {
        // 非文本内容（如图片、视频等），在搜索文本中跳过
        // 但在位置映射中占据一个位置
        textIndex += 1;
      }
    });

    // 在构建的搜索文本中查找匹配项
    let index = searchText.indexOf(query);
    let searchCount = 0;
    const maxSearches = 100;

    while (index !== -1 && searchCount < maxSearches) {
      // 将搜索文本中的位置映射回Quill位置
      if (index < indexMap.length) {
        matches.push(indexMap[index]);
      }
      index = searchText.indexOf(query, index + 1);
      searchCount++;
    }

    searchMatches.value = matches;
    currentMatchIndex.value = matches.length > 0 ? 0 : -1;

    if (matches.length > 0) {
      // 高亮第一个匹配项
      highlightMatch(matches[0], query.length);
    }
  } catch (error) {
    console.error('搜索过程中发生错误:', error);
    // 降级到简单的文本搜索
    const text = quill.getText();
    const lowerText = text.toLowerCase();
    let index = lowerText.indexOf(query);
    let searchCount = 0;
    const maxSearches = 100;

    while (index !== -1 && searchCount < maxSearches) {
      matches.push(index);
      index = lowerText.indexOf(query, index + 1);
      searchCount++;
    }

    searchMatches.value = matches;
    currentMatchIndex.value = matches.length > 0 ? 0 : -1;

    if (matches.length > 0) {
      highlightMatch(matches[0], query.length);
    }
  }
};

// 高亮并滚动到匹配项 - 改进版本，更好地处理富文本内容
const highlightMatch = (index: number, length: number) => {
  const quill = getQuillInstance();
  if (!quill) return;

  try {
    // 设置文本选择，这是最自然的"高亮"方式
    quill.setSelection(index, length, 'user');

    // 使用 setTimeout 确保选择渲染完成后再执行滚动
    setTimeout(() => {
      try {
        // 使用 Quill 的 API 获取光标所在位置的 DOM 节点
        const [leaf] = quill.getLeaf(index);

        if (leaf && leaf.domNode) {
          // leaf.domNode 可能是文本节点，需要获取其父元素
          const elementToScroll = leaf.domNode.nodeType === 3 ? leaf.domNode.parentNode : leaf.domNode;

          if (elementToScroll && typeof (elementToScroll as HTMLElement).scrollIntoView === 'function') {
            // 使用标准 scrollIntoView API，这是最可靠的滚动方法
            (elementToScroll as HTMLElement).scrollIntoView({
              behavior: 'smooth', // 平滑滚动，视觉效果更明显
              block: 'center',    // 将元素滚动到垂直方向的中心
              inline: 'nearest'   // 水平方向上就近对齐
            });
          } else {
            throw new Error('无法找到可滚动的有效 DOM 节点。');
          }
        } else {
          // 如果 getLeaf 失败，提供一个备用方案
          console.warn('Quill getLeaf API 未返回有效节点，尝试备用滚动方法。');
          const editorContainer = document.querySelector('.editor-view .ql-editor') as HTMLElement;
          const bounds = quill.getBounds(index);
          if (bounds && editorContainer) {
              const newScrollTop = bounds.top - (editorContainer.clientHeight / 2) + (bounds.height / 2);
              editorContainer.scrollTop = Math.max(0, newScrollTop);
          } else {
            throw new Error('备用滚动方法也失败了。');
          }
        }
      } catch (e) {
        console.error('滚动到搜索匹配项时发生错误:', e);
      }
    }, 100); // 100毫秒的延迟足以应对大多数渲染延迟
  } catch (e) {
    console.error('设置选择时发生错误:', e);
  }
};

const searchForward = () => {
  if (searchMatches.value.length === 0) {
    findAllMatches();
    return;
  }

  if (currentMatchIndex.value < searchMatches.value.length - 1) {
    currentMatchIndex.value++;
  } else {
    currentMatchIndex.value = 0; // 循环到第一个匹配项
    // 显示循环提示
    showNotification('已循环到第一个搜索结果');
  }

  const index = searchMatches.value[currentMatchIndex.value];
  highlightMatch(index, searchQuery.value.trim().length);
};

const searchBackward = () => {
  if (searchMatches.value.length === 0) {
    findAllMatches();
    return;
  }

  if (currentMatchIndex.value > 0) {
    currentMatchIndex.value--;
  } else {
    currentMatchIndex.value = searchMatches.value.length - 1; // 循环到最后一个匹配项
    // 显示循环提示
    showNotification('已循环到最后一个搜索结果');
  }

  const index = searchMatches.value[currentMatchIndex.value];
  highlightMatch(index, searchQuery.value.trim().length);
};

// 显示临时通知
const showNotification = (message: string) => {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = 'search-notification';
  notification.textContent = message;
  notification.style.position = 'absolute';
  notification.style.top = '60px';
  notification.style.left = '50%';
  notification.style.transform = 'translateX(-50%)';
  notification.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  notification.style.color = 'white';
  notification.style.padding = '8px 16px';
  notification.style.borderRadius = '4px';
  notification.style.zIndex = '1000';
  notification.style.opacity = '0';
  notification.style.transition = 'opacity 0.3s';
  
  // 添加到编辑器视图
  const editorView = document.querySelector('.editor-view');
  if (editorView) {
    editorView.appendChild(notification);
    
    // 显示通知
    setTimeout(() => {
      notification.style.opacity = '1';
      
      // 2秒后隐藏并移除
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 2000);
    }, 10);
  }
};

const toggleSearch = () => {
  showSearch.value = !showSearch.value;
  if (!showSearch.value) {
    // 关闭搜索时，清空搜索状态
    searchQuery.value = '';
    searchMatches.value = [];
    currentMatchIndex.value = -1;

    // 取消选择
    const quill = getQuillInstance();
    if (quill) quill.setSelection(null);
  } else {
    // 打开搜索时，聚焦搜索框
    nextTick(() => {
      const searchInput = document.querySelector('.search-input') as HTMLInputElement;
      if (searchInput) {
        // 聚焦搜索框，但不选择文本以避免干扰
        searchInput.focus();
      }
    });
  }
};

const performSearch = () => {
  // 如果搜索框为空，不执行搜索
  if (!searchQuery.value.trim()) return;

  // 执行搜索
  findAllMatches();

  // 如果有匹配项，高亮第一个
  if (searchMatches.value.length > 0) {
    currentMatchIndex.value = 0;
    const index = searchMatches.value[0];
    highlightMatch(index, searchQuery.value.trim().length);
  }
};

// 处理搜索框鼠标离开事件
const handleSearchMouseLeave = () => {
  if (searchQuery.value.trim()) {
    performSearch();
  }
};

// 监听搜索关键词变化 - 只清除之前的搜索结果，不自动搜索
watch(searchQuery, (newValue) => {
  // 只清除搜索结果，不操作Quill编辑器以避免焦点丢失
  searchMatches.value = [];
  currentMatchIndex.value = -1;
});

const toggleReminder = () => {
  showReminder.value = !showReminder.value;
  if (showReminder.value && !reminderDate.value) {
    const now = new Date();
    reminderDate.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    reminderTime.value = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
  }
};

// Event handlers
const save = () => {
  console.log('保存前的原始值:', { title: title.value, content });

  // 尝试从DOM中获取编辑器内容（备用方案）
  try {
    const editorContent = document.querySelector('.ql-editor')?.innerHTML || '';
    if (editorContent && (!content.value || content.value.trim() === '')) {
      console.log('从DOM中获取到的编辑器内容:', editorContent);
      content.value = editorContent;
    }
  } catch (error) {
    console.error('获取编辑器内容失败:', error);
  }

  // 确保提交之前进行trim操作，并处理可能的空值
  const trimmedTitle = (title.value || '').trim();
  const trimmedContent = (content.value || '').trim();

  console.log('保存的处理后的值:', { title: trimmedTitle, content: trimmedContent });

  emit('save', {
    id: props.id || '',
    title: trimmedTitle,
    content: trimmedContent,
    reminderDate: reminderDate.value || '',
    reminderTime: reminderTime.value || ''
  });
};

const cancel = () => {
  if (isContentChanged.value) {
    emit('showConfirm', {
      type: 'unsaved',
      title: '未保存的更改',
      message: '是否放弃未保存的更改？',
      onConfirm: () => emit('cancel')
    });
  } else {
    emit('cancel');
  }
};
</script>

<template>
  <div class="editor-view">
    <div class="editor-header">
      <div class="header-left">
        <button class="icon-btn cancel-icon" @click="cancel" title="取消">
          <IconCancel class="icon w-5 h-5" />
        </button>
      </div>
      <div class="header-right">
        <button class="icon-btn" @click="toggleSearch" :class="{ active: showSearch }">
          <IconSearch class="icon w-5 h-5" />
        </button>
        <button class="icon-btn" @click="toggleReminder" :class="{ active: showReminder }">
          <IconBell class="icon w-5 h-5" />
        </button>
        <button class="icon-btn save-icon" @click="save" title="保存">
          <IconSave class="icon w-5 h-5" />
        </button>
      </div>
    </div>

    <div class="search-bar" v-if="showSearch" @mouseleave="handleSearchMouseLeave">
      <input class="search-input" v-model="searchQuery" placeholder="搜索..."
        @keyup.enter="searchForward"
        @keyup.esc="toggleSearch"
        @keydown.up.prevent="searchBackward"
        @keydown.down.prevent="searchForward" />
      <div class="search-actions">
        <button class="nav-btn" @click="searchBackward" title="向上查找 (↑)">
          <span class="arrow up">↑</span>
        </button>
        <button class="nav-btn" @click="searchForward" title="向下查找 (↓)">
          <span class="arrow down">↓</span>
        </button>
        <button class="search-btn" @click="performSearch">查找</button>
        <span v-if="searchMatches.length > 0" class="match-count">
          {{ currentMatchIndex + 1 }}/{{ searchMatches.length }}
        </span>
        <button class="close-btn" @click="toggleSearch" title="关闭 (ESC)">×</button>
      </div>
    </div>

    <div class="reminder-bar" v-if="showReminder">
      <div class="reminder-inputs">
        <div class="reminder-date">
          <label>日期:</label>
          <input type="date" class="date-input" v-model="reminderDate" />
        </div>
        <div class="reminder-time">
          <label>时间:</label>
          <input type="time" class="time-input" v-model="reminderTime" />
        </div>
      </div>
    </div>

    <div class="editor-content">
      <input class="title-input" v-model="title" placeholder="标题" />
      <QuillEditor ref="quillEditorRef" v-model:content="content" :options="editorOptions" contentType="html"
        class="rich-editor" @textChange="(delta, oldDelta, source) => {
          console.log('QuillEditor 内容变更:', { delta, oldDelta, source, currentContent: content });
        }"
        @ready="onEditorReady" />
    </div>

    <!-- 移除底部的footer -->
  </div>
</template>

<style scoped>
.editor-view {
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  border-bottom: 1px solid #eef2f6;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  min-height: 44px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 15px;
  color: #64748b;
  cursor: pointer;
  padding: 5px;
  border-radius: 6px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-btn:hover {
  background-color: #f1f5f9;
  color: #334155;
  transform: translateY(-1px);
}

.icon-btn.active {
  color: #2563eb;
  background-color: rgba(37, 99, 235, 0.08);
}

.search-bar,
.reminder-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background-color: #f0f7ff;
  border-bottom: 1px solid #d0e3ff;
  animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: sticky;
  top: 0;
}

.search-bar {
  background: linear-gradient(to right, #f0f7ff, #e6f0ff);
}

.search-input {
  flex: 1;
  border: 1px solid #c0d8ff;
  border-radius: 6px;
  padding: 8px 14px;
  font-size: 15px;
  outline: none;
  background-color: white;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) inset;
  font-weight: 500;
}

.search-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}

/* 添加搜索图标 */
.search-input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: 10px center;
  padding-left: 36px;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 6px;
}

.nav-btn {
  background-color: #e9f0fd;
  border: 1px solid #c0d8ff;
  color: #3b82f6;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.nav-btn:hover {
  background-color: #dbe7fd;
  color: #2563eb;
  transform: translateY(-1px);
}

.nav-btn:active {
  transform: translateY(0);
}

.arrow {
  font-size: 16px;
  font-weight: bold;
}

.search-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(37, 99, 235, 0.3);
}

.search-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(37, 99, 235, 0.4);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(37, 99, 235, 0.3);
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #555;
  cursor: pointer;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #333;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px 10px;
  padding-bottom: 90px;
  overflow-y: auto;
  background-color: #ffffff;
  min-height: 0;
  max-height: calc(100vh - 60px);
}

.title-input {
  border: none;
  border-bottom: 2px solid #eef2f6;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
  padding: 3px 0 4px 0;
  outline: none;
  color: #1e293b;
  background-color: transparent;
  transition: border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.title-input:focus {
  border-color: #2563eb;
}

.rich-editor {
  height: auto;
  min-height: calc(100vh - 200px);
  flex: 1;
  font-size: 15px;
  line-height: 1.6;
  color: #334155;
  overflow-y: auto;
}

/* 确保 Quill 编辑器的容器能够正确显示 */
:deep(.ql-container) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 15px;
  min-height: calc(100vh - 200px);
  height: auto;
  max-height: calc(100vh - 150px);
  overflow-y: auto;
}

:deep(.ql-toolbar) {
  border-top: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
  background-color: #f8fafc;
  padding: 4px 6px !important;
  flex-shrink: 0;
}

:deep(.ql-editor) {
  min-height: calc(100vh - 250px) !important;
  padding: 12px !important;
  font-size: 15px;
  line-height: 1.6;
}

:deep(.ql-formats) {
  margin-right: 8px !important;
}

.reminder-inputs {
  display: flex;
  gap: 10px;
  flex: 1;
}

.reminder-date,
.reminder-time {
  display: flex;
  align-items: center;
  gap: 5px;
}

.reminder-date label,
.reminder-time label {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.date-input,
.time-input {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px 6px;
  font-size: 13px;
  outline: none;
  background-color: white;
  color: #334155;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.date-input:focus,
.time-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.08);
}

@keyframes slideDown {
  from {
    transform: translateY(-8px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px 10px;
  background-color: #ffffff;
  border-top: 1px solid #eef2f6;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  height: 52px;
  flex-shrink: 0;
}

.button-container {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.save-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  padding: 5px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(37, 99, 235, 0.15);
  min-width: 64px;
  flex-shrink: 0;
  height: 36px;
}

.save-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.cancel-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 5px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 64px;
  flex-shrink: 0;
  height: 36px;
}

.cancel-btn:hover {
  background: #f1f5f9;
  color: #334155;
  border-color: #cbd5e1;
}

.match-count {
  font-size: 12px;
  color: #64748b;
  margin: 0 4px;
  min-width: 40px;
  text-align: center;
}
</style>