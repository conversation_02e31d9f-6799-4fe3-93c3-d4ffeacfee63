<script lang="ts" setup>
defineProps({
  show: {
    type: Boolean,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'info',
    validator: (value: string) => ['success', 'error', 'info'].includes(value)
  }
});

const emit = defineEmits(['close']);

// 关闭通知
const closeNotification = () => {
  emit('close');
};
</script>

<template>
  <div v-if="show" class="notification" :class="type">
    <span class="notification-message">{{ message }}</span>
    <button class="notification-close" @click="closeNotification">×</button>
  </div>
</template>

<style scoped>
.notification {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 14px;
  z-index: 2000;
  animation: slideDown 0.3s ease-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.success {
  background-color: #4caf50;
}

.error {
  background-color: #f44336;
}

.info {
  background-color: #1976d2;
}

.notification-message {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  margin-left: 10px;
  opacity: 0.8;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 