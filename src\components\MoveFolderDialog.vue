<template>
  <div v-if="show" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">移动到文件夹</h3>
        <button class="close-btn" @click="cancel">×</button>
      </div>

      <div class="dialog-body">
        <div class="folder-list">
          <!-- 根目录选项 -->
          <div class="folder-option" :class="{ selected: selectedFolderId === null }" @click="selectFolder(null)">
            <span class="folder-icon">📁</span>
            <span class="folder-name">根目录</span>
          </div>

          <!-- 文件夹选项 -->
          <div v-for="folder in folders" :key="folder.id" class="folder-option"
            :class="{ selected: selectedFolderId === folder.id }" @click="selectFolder(folder.id)">
            <span class="folder-icon">📁</span>
            <span class="folder-name">{{ folder.name }}</span>
          </div>

          <!-- 空状态 -->
          <div v-if="folders.length === 0" class="empty-state">
            <span class="empty-icon">📂</span>
            <p class="empty-text">暂无文件夹</p>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="cancel">取消</button>
        <button class="btn btn-primary" :disabled="isMoving" @click="confirm">
          {{ isMoving ? '移动中...' : '确定' }}
        </button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import type { Folder } from '../types/folder.js';
import { FolderService } from '../services/FolderService.js';

interface Props {
  show: boolean;
  noteId: string;
}

const props = defineProps < Props > ();

const emit = defineEmits < {
  'confirm': [noteId: string, targetFolderId: string | null];
  'cancel': [];
} > ();

const folders = ref < Folder[] > ([]);
const selectedFolderId = ref < string | null > (null);
const isMoving = ref(false);

const loadFolders = () => {
  try {
    folders.value = FolderService.getAllFolders();
  } catch (error) {
    console.error('加载文件夹失败:', error);
    folders.value = [];
  }
};

const selectFolder = (folderId: string | null) => {
  selectedFolderId.value = folderId;
};

const confirm = () => {
  isMoving.value = true;
  emit('confirm', props.noteId, selectedFolderId.value);
};

const cancel = () => {
  emit('cancel');
  resetForm();
};

const handleOverlayClick = () => {
  cancel();
};

const resetForm = () => {
  selectedFolderId.value = null;
  isMoving.value = false;
};

watch(() => props.show, (newShow) => {
  if (newShow) {
    resetForm();
    loadFolders();
  }
});

onMounted(() => {
  loadFolders();
});

defineExpose({
  resetForm
});
</script>
<style s coped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 400px;
  max-height: 500px;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 6px;
  font-size: 20px;
  color: #666;
}

.close-btn:hover {
  background-color: #f5f5f5;
}

.dialog-body {
  padding: 20px 24px;
  max-height: 300px;
  overflow-y: auto;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.folder-option:hover {
  background-color: #f5f5f5;
}

.folder-option.selected {
  background-color: #e3f2fd;
  border-color: #1976d2;
}

.folder-icon {
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin: 0;
}

.dialog-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  min-width: 80px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e9ecef;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1565c0;
}
</style>