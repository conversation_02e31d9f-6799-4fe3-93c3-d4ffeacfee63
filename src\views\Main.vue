<script lang="ts" setup>
import { ref } from 'vue';
import Notes from './Notes.vue';
import Todos from './Todos.vue';
import BottomNav from '../components/BottomNav.vue';
import DataManagement from '../components/DataManagement.vue';

// 当前视图
const currentView = ref<'notes' | 'todos' | 'data'>('notes');

// 切换视图
const switchView = (view: string) => {
  if (view === 'notes' || view === 'todos' || view === 'data') {
    currentView.value = view as 'notes' | 'todos' | 'data';
  }
};

// 从外部传入的参数
defineProps({
  enterAction: {
    type: Object,
    required: true
  }
});
</script>

<template>
  <div class="app-container">
    <!-- 根据当前视图显示不同内容 -->
    <Notes v-if="currentView === 'notes'" />
    <Todos v-else-if="currentView === 'todos'" />
    <div v-else-if="currentView === 'data'" class="data-view">
      
      <DataManagement />
    </div>

    <!-- 底部导航栏 -->
    <BottomNav :current-view="currentView" @switch-view="switchView" />
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.data-view {
  padding: 16px;
  height: calc(100vh - 80px);
  overflow: hidden;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.data-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  border-radius: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.header-icon {
  font-size: 48px;
  opacity: 0.9;
}

.header-content {
  text-align: left;
}

.data-header h1 {
  font-size: 28px;
  margin-bottom: 8px;
  color: white;
  font-weight: 600;
}

.data-header p {
  font-size: 16px;
  margin: 0 0 4px 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.header-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.data-header p {
  font-size: 16px;
  color: #666;
}
</style>