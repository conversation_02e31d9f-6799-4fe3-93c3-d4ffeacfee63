<template>
  <div class="folder-sidebar">
    <!-- 文件夹头部 -->
    <div class="folder-header">
      <h3 class="folder-title">文件夹</h3>
      <button 
        @click="handleCreateFolder" 
        class="create-folder-btn"
        :disabled="isLoading"
        title="创建文件夹 (Ctrl+Shift+N)"
      >
        <span class="icon" v-if="!isLoading">+</span>
        <span class="loading-spinner" v-else></span>
      </button>
    </div>

    <!-- 文件夹搜索 -->
    <div class="folder-search" v-if="folderTree.length > 5">
      <input
        v-model="searchQuery"
        type="text"
        class="search-input"
        placeholder="搜索文件夹..."
        @keydown.esc="searchQuery = ''"
      />
      <span class="search-icon">🔍</span>
    </div>

    <!-- 全局统计信息 -->
    <div class="global-stats" v-if="globalStats">
      <div class="stats-row">
        <span class="stats-label">📁 文件夹:</span>
        <span class="stats-value">{{ globalStats.totalFolders }}</span>
      </div>
      <div class="stats-row">
        <span class="stats-label">📝 笔记:</span>
        <span class="stats-value">{{ globalStats.totalNotes }}</span>
      </div>
      <div class="stats-row" v-if="globalStats.emptyFolders > 0">
        <span class="stats-label">📂 空文件夹:</span>
        <span class="stats-value empty-folders">{{ globalStats.emptyFolders }}</span>
      </div>
    </div>
    
    <!-- 文件夹列表 -->
    <div class="folder-list">
      <!-- 根目录项 -->
      <div 
        class="folder-item root" 
        :class="{ 
          active: currentFolderId === null,
          'drag-over': isRootDragOver
        }"
        @click="selectFolder(null)"
        @dragover.prevent="handleRootDragOver"
        @dragleave="handleRootDragLeave"
        @drop.prevent="handleRootDrop"
      >
        <span class="folder-icon">📁</span>
        <span class="folder-name">所有笔记</span>
        <span class="note-count">({{ totalNoteCount }})</span>
        <div v-if="isRootDragOver" class="drag-indicator"></div>
      </div>
      
      <!-- 文件夹树 -->
      <FolderTreeItem 
        v-for="node in filteredFolderTree" 
        :key="node.folder.id"
        :node="node"
        :current-folder-id="currentFolderId"
        :expanded-folders="expandedFolders"
        @select-folder="selectFolder"
        @toggle-expand="toggleExpand"
        @context-menu="$emit('folder-context-menu', $event)"
        @drop-note="$emit('drop-note', $event)"
      />
      
      <!-- 空状态 -->
      <div v-if="folderTree.length === 0" class="empty-state">
        <span class="empty-icon">📂</span>
        <p class="empty-text">暂无文件夹</p>
        <p class="empty-hint">点击 + 创建第一个文件夹</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import type { FolderTreeNode } from '../types/folder.js';
import { FolderService } from '../services/FolderService.js';
import FolderTreeItem from './FolderTreeItem.vue';

// Props
interface Props {
  currentFolderId?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  currentFolderId: null
});

// Emits
const emit = defineEmits<{
  'select-folder': [folderId: string | null];
  'create-folder': [];
  'folder-context-menu': [event: { folderId: string; position: { x: number; y: number } }];
  'drop-note': [event: { noteId: string; targetFolderId: string | null }];
}>();

// 响应式数据
const folderTree = ref<FolderTreeNode[]>([]);
const expandedFolders = ref<Set<string>>(new Set());
const totalNoteCount = ref<number>(0);
const isRootDragOver = ref<boolean>(false);
const globalStats = ref<any>(null);
const isLoading = ref<boolean>(false);
const searchQuery = ref<string>('');

// 文件夹展开状态持久化键
const EXPANDED_FOLDERS_KEY = 'expandedFolders';

// 方法
const loadFolderTree = () => {
  try {
    folderTree.value = FolderService.getFolderTree();
    // 计算总笔记数量
    const notes = utools.dbStorage.getItem('notes');
    totalNoteCount.value = notes ? JSON.parse(notes).length : 0;
    // 加载全局统计信息
    loadGlobalStats();
    // 不再自动加载展开状态，保持默认不展开
  } catch (error) {
    console.error('加载文件夹树失败:', error);
    folderTree.value = [];
    totalNoteCount.value = 0;
    globalStats.value = null;
  }
};

// 加载文件夹展开状态
const loadExpandedFolders = () => {
  // 强制默认不展开任何文件夹
  expandedFolders.value = new Set();
  console.log('loadExpandedFolders: 设置所有文件夹为不展开状态');
};

// 保存文件夹展开状态
const saveExpandedFolders = () => {
  try {
    const expandedArray = Array.from(expandedFolders.value);
    utools.dbStorage.setItem(EXPANDED_FOLDERS_KEY, JSON.stringify(expandedArray));
  } catch (error) {
    console.error('保存文件夹展开状态失败:', error);
  }
};

// 重置所有文件夹为不展开状态
const resetExpandedFolders = () => {
  expandedFolders.value.clear();
  saveExpandedFolders();
};

const loadGlobalStats = () => {
  try {
    globalStats.value = FolderService.getGlobalStats();
  } catch (error) {
    console.error('加载全局统计失败:', error);
    globalStats.value = null;
  }
};

const selectFolder = (folderId: string | null) => {
  emit('select-folder', folderId);
};

const toggleExpand = (folderId: string) => {
  if (expandedFolders.value.has(folderId)) {
    expandedFolders.value.delete(folderId);
  } else {
    expandedFolders.value.add(folderId);
  }
  // 保存展开状态
  saveExpandedFolders();
};

// 根目录拖拽处理方法
const handleRootDragOver = (event: DragEvent) => {
  if (event.dataTransfer?.types.includes('text/note-id')) {
    event.preventDefault();
    isRootDragOver.value = true;
    event.dataTransfer.dropEffect = 'move';
  }
};

const handleRootDragLeave = (event: DragEvent) => {
  // 只有当鼠标真正离开元素时才清除高亮
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isRootDragOver.value = false;
  }
};

const handleRootDrop = (event: DragEvent) => {
  event.preventDefault();
  isRootDragOver.value = false;
  
  const noteId = event.dataTransfer?.getData('text/note-id');
  
  if (noteId) {
    emit('drop-note', {
      noteId,
      targetFolderId: null // null 表示移动到根目录
    });
  }
};

// 处理创建文件夹
const handleCreateFolder = () => {
  if (isLoading.value) return;
  emit('create-folder');
};

// 文件夹搜索过滤
const filteredFolderTree = computed(() => {
  if (!searchQuery.value.trim()) {
    return folderTree.value;
  }
  
  const query = searchQuery.value.toLowerCase();
  
  const filterNodes = (nodes: FolderTreeNode[]): FolderTreeNode[] => {
    return nodes.filter(node => {
      const nameMatch = node.folder.name.toLowerCase().includes(query);
      const hasMatchingChildren = node.children.length > 0 && filterNodes(node.children).length > 0;
      
      if (nameMatch || hasMatchingChildren) {
        return {
          ...node,
          children: filterNodes(node.children)
        };
      }
      return false;
    }).map(node => ({
      ...node,
      children: filterNodes(node.children)
    }));
  };
  
  return filterNodes(folderTree.value);
});

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+Shift+N: 创建文件夹
  if (event.ctrlKey && event.shiftKey && event.key === 'N') {
    event.preventDefault();
    handleCreateFolder();
  }
  
  // Escape: 清空搜索
  if (event.key === 'Escape' && searchQuery.value) {
    searchQuery.value = '';
  }
};

// 生命周期
onMounted(() => {
  // 确保文件夹默认不展开
  expandedFolders.value = new Set();
  console.log('onMounted: 设置 expandedFolders 为空:', Array.from(expandedFolders.value));

  // 清除之前保存的展开状态
  utools.dbStorage.removeItem(EXPANDED_FOLDERS_KEY);

  // 加载文件夹树
  loadFolderTree();
  
  // 再次确保展开状态为空（防止 loadFolderTree 中的其他逻辑影响）
  expandedFolders.value.clear();

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown);
});

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// 刷新文件夹树并保持不展开状态
const refreshFolderTree = () => {
  loadFolderTree();
  // 确保刷新后文件夹仍然不展开
  expandedFolders.value.clear();
};

// 暴露方法给父组件
defineExpose({
  refreshFolderTree,
  resetExpandedFolders
});
</script>

<style scoped>
.folder-sidebar {
  width: 100px !important;
  height: 100%;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.folder-header {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
}

.folder-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.create-folder-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: #1976d2;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.create-folder-btn:hover {
  background-color: #1565c0;
  transform: scale(1.05);
}

.create-folder-btn .icon {
  font-size: 16px;
  font-weight: bold;
}

/* 全局统计信息样式 */
.global-stats {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-value {
  color: #333;
  font-weight: 600;
  font-size: 11px;
}

.stats-value.empty-folders {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
}

.folder-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}

.folder-item:hover {
  background-color: #e3f2fd;
}

.folder-item.active {
  background-color: #1976d2;
  color: white;
}

.folder-item.root {
  font-weight: 500;
  margin-bottom: 8px;
}

.folder-icon {
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.folder-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-count {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
  flex-shrink: 0;
}

.folder-item.active .note-count {
  color: rgba(255, 255, 255, 0.8);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  margin: 0;
  opacity: 0.7;
}

/* 根目录拖拽状态样式 */
.folder-item.root.drag-over {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
  border: 2px dashed #4caf50;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
  animation: rootDragPulse 1s infinite alternate;
  position: relative;
}

.folder-item.root.drag-over .folder-icon {
  animation: bounce 0.6s infinite alternate;
}

.folder-item.root.drag-over .folder-name {
  font-weight: 600;
  color: #4caf50;
}

.drag-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #4caf50;
  border-radius: 1px;
}

@keyframes rootDragPulse {
  0% {
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
  }
  100% {
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-2px);
  }
}

/* 文件夹搜索样式 */
.folder-search {
  position: relative;
  padding: 8px 16px;
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  width: 100%;
  padding: 6px 30px 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.search-icon {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #999;
  pointer-events: none;
}

/* 加载状态样式 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.create-folder-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.create-folder-btn:disabled:hover {
  transform: none;
  background-color: #1976d2;
}

/* 滚动条样式 */
.folder-list::-webkit-scrollbar {
  width: 6px;
}

.folder-list::-webkit-scrollbar-track {
  background: transparent;
}

.folder-list::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.folder-list::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

/* 确保在大屏幕上宽度设置生效 */
@media (min-width: 769px) {
  .folder-sidebar {
    width: 160px !important;
  }
}
</style>