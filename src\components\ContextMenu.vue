<script lang="ts" setup>
defineProps({
  show: {
    type: Boolean,
    required: true
  },
  position: {
    type: Object,
    required: true,
    validator: (value: any) => 'x' in value && 'y' in value
  },
  isPinned: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'note',
    validator: (value: string) => ['note', 'todo', 'subtodo'].includes(value)
  }
});

const emit = defineEmits([
  'toggle-pin', 
  'delete', 
  'add-to-todo', 
  'move-to-folder',
  'quick-add', 
  'batch-add', 
  'complete-all', 
  'clear-all'
]);

// 切换置顶状态
const togglePin = () => {
  emit('toggle-pin');
};

// 删除项目
const deleteItem = () => {
  emit('delete');
};

// 添加到待办
const addToTodo = () => {
  emit('add-to-todo');
};

// 快速添加子项
const quickAdd = () => {
  emit('quick-add');
};

// 批量添加子项
const batchAdd = () => {
  emit('batch-add');
};

// 完成所有子项
const completeAll = () => {
  emit('complete-all');
};

// 清空所有子项
const clearAll = () => {
  emit('clear-all');
};

// 移动到文件夹
const moveToFolder = () => {
  emit('move-to-folder');
};
</script>

<template>
  <div v-if="show" class="context-menu" :style="{ top: position.y + 'px', left: position.x + 'px' }" @click.stop>
    <!-- 普通笔记/待办菜单 -->
    <template v-if="type === 'note' || type === 'todo'">
      <div class="context-menu-item" @click="togglePin">
        {{ isPinned ? '取消置顶' : (type === 'note' ? '置顶笔记' : '置顶待办') }}
      </div>
      <div v-if="type === 'note'" class="context-menu-item" @click="addToTodo">
        添加到待办
      </div>
      <div v-if="type === 'note'" class="context-menu-item" @click="moveToFolder">
        <span class="menu-icon">📁</span> 移动到文件夹
      </div>
      <div class="context-menu-item delete" @click="deleteItem">
        {{ type === 'note' ? '删除笔记' : '删除待办' }}
      </div>
    </template>
    
    <!-- 子项管理菜单 -->
    <template v-else-if="type === 'subtodo'">
      <div class="context-menu-item" @click="quickAdd">
        <span class="menu-icon">+</span> 快速添加子项
      </div>
      <div class="context-menu-item" @click="batchAdd">
        <span class="menu-icon">++</span> 批量添加子项
      </div>
      <div class="context-menu-item" @click="completeAll">
        <span class="menu-icon">✓</span> 完成所有子项
      </div>
      <div class="context-menu-item delete" @click="clearAll">
        <span class="menu-icon">🗑️</span> 清空所有子项
      </div>
    </template>
  </div>
</template>

<style scoped>
.context-menu {
  position: fixed;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  padding: 4px 0;
  z-index: 1000;
  min-width: 160px;
  animation: fadeIn 0.15s ease-out;
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item.delete {
  color: #f44336;
}

.context-menu-item.delete:hover {
  background-color: #ffebee;
}

.menu-icon {
  margin-right: 8px;
  font-size: 14px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 