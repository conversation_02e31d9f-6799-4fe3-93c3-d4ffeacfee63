:root {
  --primary-blue: #1976d2;
  --primary-blue-hover: #1565c0;
  --light: #fff;
  --background: #f8f9fa;
  --text-primary: #1a1a1a;
  --text-secondary: #666;
  --border: #e0e0e0;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

button {
  border: none;
  background: var(--primary-blue);
  color: var(--light);
  line-height: 2.5;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

button:disabled {
  filter: grayscale(1);
  cursor: not-allowed;
  opacity: 0.6;
}

button:not(:disabled):hover {
  background: var(--primary-blue-hover);
  transform: translateY(-1px);
}

button:not(:disabled):active {
  opacity: 0.8;
  transform: translateY(0);
}

textarea {
  display: block;
  margin: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (prefers-color-scheme: light) {
  body {
    background-color: var(--background);
    color: var(--text-primary);
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border: #333333;
  }

  ::-webkit-scrollbar-track {
    background: #2a2a2a;
  }

  ::-webkit-scrollbar-thumb {
    background: #555;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #666;
  }

  body {
    background-color: var(--background);
    color: var(--text-primary);
  }
}
