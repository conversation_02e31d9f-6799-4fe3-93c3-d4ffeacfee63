/**
 * 数据管理工具类 - 处理笔记和待办数据的导入导出
 */

// 定义导出数据的格式
interface ExportData {
  notes?: any[];
  todos?: any[];
  exportDate: string;
  version: string;
}

/**
 * 数据管理工具类
 */
export default class DataManager {
  /**
   * 导出全部数据（笔记和待办）
   * @returns 包含所有数据的JSON字符串
   */
  static exportAllData(): string {
    const notes = utools.dbStorage.getItem('notes') ? JSON.parse(utools.dbStorage.getItem('notes') || '[]') : [];
    const todos = utools.dbStorage.getItem('todos') ? JSON.parse(utools.dbStorage.getItem('todos') || '[]') : [];

    const exportData: ExportData = {
      notes,
      todos,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导出笔记数据
   * @returns 包含笔记数据的JSON字符串
   */
  static exportNotes(): string {
    const notes = utools.dbStorage.getItem('notes') ? JSON.parse(utools.dbStorage.getItem('notes') || '[]') : [];

    const exportData: ExportData = {
      notes,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导出待办数据
   * @returns 包含待办数据的JSON字符串
   */
  static exportTodos(): string {
    const todos = utools.dbStorage.getItem('todos') ? JSON.parse(utools.dbStorage.getItem('todos') || '[]') : [];

    const exportData: ExportData = {
      todos,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 将数据保存为文件并下载
   * @param data 要导出的数据字符串
   * @param filename 文件名
   */
  static saveToFile(data: string, filename: string): void {
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 导入数据
   * @param jsonData JSON格式的数据字符串
   * @param options 导入选项 { mergeNotes, mergeTodos } - 是否合并现有数据
   * @returns 导入结果信息
   */
  static importData(jsonData: string, options = { mergeNotes: false, mergeTodos: false }): { success: boolean, message: string } {
    try {
      const data: ExportData = JSON.parse(jsonData);

      // 验证数据格式
      if (!data.exportDate || !data.version) {
        return { success: false, message: '无效的数据格式' };
      }

      let result = '';

      // 导入笔记
      if (data.notes && Array.isArray(data.notes)) {
        if (options.mergeNotes) {
          // 合并现有笔记
          const existingNotes = utools.dbStorage.getItem('notes') ? JSON.parse(utools.dbStorage.getItem('notes') || '[]') : [];
          const mergedNotes = [...existingNotes];

          // 根据ID去重，保留最新的
          data.notes.forEach(newNote => {
            const existingIndex = mergedNotes.findIndex(note => note.id === newNote.id);
            if (existingIndex !== -1) {
              mergedNotes[existingIndex] = newNote;
            } else {
              mergedNotes.push(newNote);
            }
          });

          utools.dbStorage.setItem('notes', JSON.stringify(mergedNotes));
          result += `合并了 ${data.notes.length} 条笔记`;
        } else {
          // 替换现有笔记
          utools.dbStorage.setItem('notes', JSON.stringify(data.notes));
          result += `导入了 ${data.notes.length} 条笔记`;
        }
      }

      // 导入待办
      if (data.todos && Array.isArray(data.todos)) {
        if (options.mergeTodos) {
          // 合并现有待办
          const existingTodos = utools.dbStorage.getItem('todos') ? JSON.parse(utools.dbStorage.getItem('todos') || '[]') : [];
          const mergedTodos = [...existingTodos];

          // 根据ID去重，保留最新的
          data.todos.forEach(newTodo => {
            const existingIndex = mergedTodos.findIndex(todo => todo.id === newTodo.id);
            if (existingIndex !== -1) {
              mergedTodos[existingIndex] = newTodo;
            } else {
              mergedTodos.push(newTodo);
            }
          });

          utools.dbStorage.setItem('todos', JSON.stringify(mergedTodos));
          result += result ? `, 合并了 ${data.todos.length} 条待办` : `合并了 ${data.todos.length} 条待办`;
        } else {
          // 替换现有待办
          utools.dbStorage.setItem('todos', JSON.stringify(data.todos));
          result += result ? `, 导入了 ${data.todos.length} 条待办` : `导入了 ${data.todos.length} 条待办`;
        }
      }

      return { success: true, message: result || '没有可导入的数据' };
    } catch (error) {
      console.error('导入数据失败:', error);
      return { success: false, message: '导入失败，数据格式错误' };
    }
  }

  /**
   * 清除所有数据
   * @returns 操作结果
   */
  static clearAllData(): { success: boolean, message: string } {
    try {
      // 清除本地存储数据
      utools.dbStorage.removeItem('notes');
      utools.dbStorage.removeItem('todos');
      utools.dbStorage.removeItem('compactTodoMode'); // 清除紧凑模式设置

      // 调用utools API清除数据
      if (typeof utools !== 'undefined' && utools.db) {
        // 清除utools数据库中的相关数据
        try {
          utools.db.remove('notes');
          utools.db.remove('todos');
          utools.db.remove('compactTodoMode');

          // 清除所有以notes或todos开头的数据项
          const allDocs = utools.db.allDocs();
          if (allDocs && Array.isArray(allDocs)) {
            allDocs.forEach((doc: any) => {
              if (doc._id && (doc._id.startsWith('notes') || doc._id.startsWith('todos'))) {
                try {
                  utools.db.remove(doc._id);
                } catch (removeError) {
                  console.warn(`清除数据项 ${doc._id} 时出现警告:`, removeError);
                }
              }
            });
          }
        } catch (dbError) {
          console.warn('清除utools数据库数据时出现警告:', dbError);
          // 不阻断主要流程，只记录警告
        }
      }

      return { success: true, message: '所有数据已清除（包括本地存储和utools数据库）' };
    } catch (error) {
      console.error('清除数据失败:', error);
      return { success: false, message: '清除数据失败' };
    }
  }
} 