<script lang="ts" setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';

// 统一定义待办项类型
interface ItemType {
  id: string;
  title: string;
  content?: string;
  completed?: boolean;
  date?: string;
  parentId?: string;
  reminderDate?: string;
  reminderTime?: string;
}

const props = defineProps<{
  id?: string;
  initialTitle?: string;
  initialContent?: string;
  initialItems?: ItemType[];
  contentPlaceholder?: string;
  type: 'todo-list' | 'todo';
}>();

const emit = defineEmits(['save', 'cancel', 'showConfirm', 'toggle-continuous-add']);

// 修改item类型定义
const items = ref<ItemType[]>([]);

const newItem = ref('');
const title = ref(props.initialTitle || '');
const content = ref(props.initialContent || '');
const continuousAdd = ref(false);

// 记录原始内容，用于检测变更
const originalTitle = ref<string>(props.initialTitle || '');
const originalContent = ref<string>(props.initialContent || '');
const originalItems = ref<ItemType[]>([]);

const itemRefs = ref<Record<string, HTMLInputElement>>({});
const newItemInput = ref<HTMLInputElement | null>(null);

const showConfirmDialog = (message: string, onConfirm: () => void, onCancel: () => void) => {
  emit('showConfirm', { message, onConfirm, onCancel });
};

const handleCancel = () => {
  if (isContentChanged.value) {
    emit('showConfirm', {
      message: '确定要取消编辑吗？未保存的内容将会丢失。',
      onConfirm: () => emit('cancel')
    });
  } else {
    emit('cancel');
  }
};

// 确保在组件挂载后正确初始化原始值和items
onMounted(() => {
  originalTitle.value = props.initialTitle || '';
  originalContent.value = props.initialContent || '';

  if (props.initialItems && props.initialItems.length > 0) {
    // 直接使用传入的对象数组
    items.value = props.initialItems.map(item => ({
      ...item,
      id: item.id || Date.now().toString() + Math.random().toString(36).substr(2, 5),
      title: item.title || '',
      completed: item.completed || false
    }));
    originalItems.value = JSON.parse(JSON.stringify(items.value));
  } else {
    items.value = [];
    originalItems.value = [];
  }
});

onUnmounted(() => {
  // Clean up if needed
});

const addItem = () => {
  if (newItem.value.trim()) {
    const newId = Date.now().toString();
    items.value.push({
      id: newId,
      title: newItem.value.trim(),
      completed: false,
    });
    newItem.value = '';
    
    nextTick(() => {
      const lastItemRef = itemRefs.value[newId];
      if(lastItemRef) {
        lastItemRef.focus();
      }
    });
  }
};

const removeItem = (index: number) => {
  items.value.splice(index, 1);
};

const handleSave = () => {
  emit('save', {
    id: props.id,
    title: title.value,
    content: content.value, // Also emit content for non-list types
    items: items.value
  });
};

// 检查内容是否被修改
const isContentChanged = computed(() => {
  const currentTitle = title.value || '';
  const currentContent = content.value || '';
  
  const isTitleChanged = currentTitle.trim() !== originalTitle.value.trim();
  const isContentChanged = currentContent.trim() !== originalContent.value.trim();
  const isItemsChanged = JSON.stringify(items.value) !== JSON.stringify(originalItems.value);
  
  if (props.type === 'todo') {
    return isTitleChanged || isItemsChanged;
  }
  
  return isTitleChanged || isContentChanged;
});

const toggleContinuousAdd = () => {
  continuousAdd.value = !continuousAdd.value;
  emit('toggle-continuous-add', continuousAdd.value);
}

const focusNextOrAddNew = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    if (index < items.value.length - 1) {
      const nextItem = items.value[index + 1];
      const nextRef = itemRefs.value[nextItem.id];
      if (nextRef) {
        nextRef.focus();
      }
    } else {
      newItemInput.value?.focus();
    }
  }
};

const addItemAndFocus = () => {
  if (newItem.value.trim()) {
    const newId = Date.now().toString();
    items.value.push({
      id: newId,
      title: newItem.value.trim(),
      completed: false,
    });
    newItem.value = '';
  }
  nextTick(() => {
      newItemInput.value?.focus();
  });
};

</script>

<template>
  <div class="editor-page" :class="{ 'with-list': type === 'todo' }">
    <div class="editor-header">
      <button @click="handleCancel" class="header-btn back-btn">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
          <path d="M11.9997 10.5865L16.9495 5.63672L18.3637 7.05093L13.4139 12.0007L18.3637 16.9504L16.9495 18.3646L11.9997 13.4148L7.04996 18.3646L5.63574 16.9504L10.5855 12.0007L5.63574 7.05093L7.04996 5.63672L11.9997 10.5865Z"></path>
        </svg>
        <span>关闭</span>
      </button>
      <div class="header-actions">
        <!-- <button v-if="type === 'todo'" @click="toggleContinuousAdd" class="header-btn" :class="{ active: continuousAdd }">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="18" height="18">
            <path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path>
          </svg>
          <span>连续添加</span>
        </button> -->
        <button @click="handleSave" class="header-btn save-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="14" height="14">
            <path d="M7 19V13H17V19H19V11H5V19H7ZM6.16667 12.5L5 11.3333L12 4.33333L19 11.3333L17.8333 12.5L12 6.66667L6.16667 12.5Z"></path>
          </svg>
          <span>保存</span>
        </button>
      </div>
    </div>

    <div class="editor-main">
      <div class="scrollable-content">
        <div class="title-container">
          <input v-model="title" :placeholder="type === 'todo' ? '待办标题...' : '清单标题...'" class="title-input" />
        </div>

        <div class="content-container">
          <div v-if="type === 'todo'" class="todo-list">
            <div v-for="(item, index) in items" :key="item.id" class="todo-item">
              <input type="checkbox" v-model="item.completed" class="todo-checkbox" />
              <input :ref="el => { if (el) itemRefs[item.id] = el as HTMLInputElement }" v-model="item.title" @keydown.enter="focusNextOrAddNew(index, $event)" placeholder="新子项..." class="todo-title-input" />
              <button @click="removeItem(index)" class="remove-item-btn">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="12" height="12">
                  <path d="M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM9 4V6H15V4H9ZM18 8H6V20H18V8Z"></path>
                </svg>
              </button>
            </div>
          </div>

          <textarea v-else v-model="content" :placeholder="contentPlaceholder || '输入内容...'"
            class="content-textarea"></textarea>
        </div>
      </div>
       <div v-if="type === 'todo'" class="add-item-row">
        <input ref="newItemInput" v-model="newItem" @keydown.enter="addItemAndFocus" placeholder="添加新子项..." class="add-item-input" />
        <button @click="addItemAndFocus" class="add-item-btn">添加</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.editor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
  animation: slideInUp 0.3s ease-out;
  padding-bottom: 120px; /* 为底部导航栏预留空间 */
  box-sizing: border-box; /* 确保 padding 不会增加总高度 */
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0,0,0,0.04);
  z-index: 10;
  min-height: 44px;
}

.header-actions {
  display: flex;
  gap: 4px;
}

.header-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 28px;
}

.header-btn:hover {
  background-color: #f1f3f5;
}

.header-btn.active {
  background-color: #e3fafc;
  color: #15aabf;
}

.save-btn {
  background-color: #3b82f6;
  color: #fff;
}

.save-btn:hover {
  background-color: #2563eb;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden; /* Changed from auto */
  position: relative; /* 为绝对定位的子元素提供上下文 */
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px;
  padding-bottom: 60px; /* 为底部的添加栏预留空间 */
}

.title-container {
  margin-bottom: 12px;
}

.title-input {
  width: 100%;
  padding: 8px 0;
  font-size: 18px;
  font-weight: 600;
  border: none;
  border-bottom: 1px solid #e9ecef;
  background-color: transparent;
  outline: none;
  color: #212529;
  transition: border-color 0.2s ease;
}

.title-input:focus {
  border-bottom-color: #3b82f6;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
  padding: 12px;
  margin: 0 -12px;
  border-top: 1px solid #e9ecef;
  background-color: #fff;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  min-height: 32px;
}

.todo-checkbox {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.todo-title-input {
  flex: 1;
  border: none;
  background-color: transparent;
  font-size: 13px;
  outline: none;
  padding: 2px 0;
}

.remove-item-btn {
  background: none;
  border: none;
  color: #adb5bd;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 20px;
  min-height: 20px;
}

.remove-item-btn:hover {
  background-color: #f1f3f5;
  color: #495057;
}

.add-item-row {
  display: flex;
  gap: 6px;
  padding: 8px 12px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  position: fixed;
  bottom: 80px;
  left: 0;
  right: 0;
  min-height: 44px;
  align-items: center;
  z-index: 10;
}

.add-item-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  height: 28px;
  box-sizing: border-box;
}

.add-item-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.add-item-btn {
  padding: 6px 12px;
  border: none;
  background-color: #20c997;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: 28px;
  white-space: nowrap;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-item-btn:hover {
  background-color: #12b886;
}

.content-textarea {
  flex: 1;
  width: 100%;
  padding: 12px;
  font-size: 16px;
  line-height: 1.7;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background-color: #fff;
  outline: none;
  resize: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.content-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}
</style>