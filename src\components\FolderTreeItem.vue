<template>
  <div class="folder-tree-item">
    <!-- 文件夹项 -->
    <div 
      class="folder-item"
      :class="{ 
        active: currentFolderId === node.folder.id,
        'has-children': node.children.length > 0,
        'drag-over': isDragOver
      }"
      :style="{ paddingLeft: `${depth * 16 + 12}px` }"
      @click="selectFolder"
      @contextmenu.prevent="showContextMenu"
      @dragover.prevent="handleDragOver"
      @dragleave="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <!-- 展开/收起按钮 -->
      <button 
        v-if="node.children.length > 0"
        class="expand-btn"
        @click.stop="toggleExpand"
      >
        <span class="expand-icon" :class="{ expanded: isExpanded }">▶</span>
      </button>
      <div v-else class="expand-placeholder"></div>
      
      <!-- 文件夹图标 -->
      <span class="folder-icon">
        {{ node.folder.icon || '📁' }}
      </span>
      
      <!-- 文件夹名称 -->
      <span class="folder-name" :title="node.folder.name">
        {{ node.folder.name }}
      </span>
      
      <!-- 笔记数量和状态 -->
      <span class="note-count" v-if="node.noteCount > 0">
        ({{ node.noteCount }})
      </span>
      <span class="empty-indicator" v-else-if="node.children.length === 0" title="空文件夹">
        📂
      </span>
      
      <!-- 拖拽指示器 -->
      <div v-if="isDragOver" class="drag-indicator"></div>
    </div>
    
    <!-- 子文件夹 -->

    <div v-if="isExpanded && node.children.length > 0" class="children">
      <FolderTreeItem
        v-for="child in node.children"
        :key="child.folder.id"
        :node="child"
        :current-folder-id="currentFolderId"
        :expanded-folders="expandedFolders"
        :depth="depth + 1"
        @select-folder="$emit('select-folder', $event)"
        @toggle-expand="$emit('toggle-expand', $event)"
        @context-menu="$emit('context-menu', $event)"
        @drop-note="$emit('drop-note', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import type { FolderTreeNode } from '../types/folder.js';

// Props
interface Props {
  node: FolderTreeNode;
  currentFolderId?: string | null;
  expandedFolders: Set<string>;
  depth?: number;
}

const props = withDefaults(defineProps<Props>(), {
  currentFolderId: null,
  depth: 0
});

// Emits
const emit = defineEmits<{
  'select-folder': [folderId: string];
  'toggle-expand': [folderId: string];
  'context-menu': [event: { folderId: string; position: { x: number; y: number } }];
  'drop-note': [event: { noteId: string; targetFolderId: string }];
}>();

// 响应式数据
const isDragOver = ref(false);

// 计算属性
const isExpanded = computed(() => {
  return props.expandedFolders.has(props.node.folder.id);
});

// 方法
const selectFolder = () => {
  emit('select-folder', props.node.folder.id);
};

const toggleExpand = () => {
  emit('toggle-expand', props.node.folder.id);
};

const showContextMenu = (event: MouseEvent) => {
  emit('context-menu', {
    folderId: props.node.folder.id,
    position: {
      x: event.clientX,
      y: event.clientY
    }
  });
};

const handleDragOver = (event: DragEvent) => {
  if (event.dataTransfer?.types.includes('text/note-id')) {
    event.preventDefault();
    isDragOver.value = true;
    event.dataTransfer.dropEffect = 'move';
  }
};

const handleDragLeave = (event: DragEvent) => {
  // 只有当鼠标真正离开元素时才清除高亮
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;
  
  const noteId = event.dataTransfer?.getData('text/note-id');
  const noteTitle = event.dataTransfer?.getData('text/note-title');
  
  if (noteId) {
    emit('drop-note', {
      noteId,
      targetFolderId: props.node.folder.id,
      noteTitle: noteTitle || '未知笔记'
    });
  }
};
</script>

<style scoped>
.folder-tree-item {
  position: relative;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1px;
  position: relative;
  min-height: 32px;
}

.folder-item:hover {
  background-color: #e3f2fd;
}

.folder-item.active {
  background-color: #1976d2;
  color: white;
}

.folder-item.has-children {
  /* 为有子文件夹的项目添加特殊样式 */
}

.expand-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  padding: 0;
  color: inherit;
}

.expand-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.folder-item.active .expand-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.expand-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.expand-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
  display: inline-block;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.folder-icon {
  font-size: 14px;
  margin-right: 8px;
  flex-shrink: 0;
}

.folder-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400;
}

.note-count {
  font-size: 11px;
  color: #666;
  margin-left: 4px;
  flex-shrink: 0;
  font-weight: normal;
}

.folder-item.active .note-count {
  color: rgba(255, 255, 255, 0.8);
}

.empty-indicator {
  font-size: 10px;
  color: #ccc;
  margin-left: 4px;
  flex-shrink: 0;
  opacity: 0.6;
}

.folder-item.active .empty-indicator {
  color: rgba(255, 255, 255, 0.5);
}

.children {
  /* 子文件夹容器 */
  display: block;
}

.drag-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1976d2;
  border-radius: 1px;
}

/* 拖拽状态样式 */
.folder-item.drag-over {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 2px dashed #1976d2;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
  animation: dragPulse 1s infinite alternate;
}

.folder-item.drag-over .folder-icon {
  animation: bounce 0.6s infinite alternate;
}

.folder-item.drag-over .folder-name {
  font-weight: 600;
  color: #1976d2;
}

@keyframes dragPulse {
  0% {
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
  }
  100% {
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-2px);
  }
}

/* 深度缩进样式 */
.folder-item[style*="padding-left"] {
  border-left: 1px solid transparent;
}

.folder-item:hover[style*="padding-left"] {
  border-left-color: #e0e0e0;
}

.folder-item.active[style*="padding-left"] {
  border-left-color: rgba(255, 255, 255, 0.3);
}
</style>