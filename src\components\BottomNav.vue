<script lang="ts" setup>
defineProps({
  currentView: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['switch-view']);

// 切换视图
const switchView = (view: string) => {
  emit('switch-view', view);
};
</script>

<template>
  <div class="bottom-nav">
    <div class="nav-container">
      <div class="nav-item" :class="{ 'active': currentView === 'notes' }" @click="switchView('notes')">
        <span class="nav-icon">📝</span>
        <span class="nav-text">笔记</span>
      </div>
      <div class="nav-item" :class="{ 'active': currentView === 'todos' }" @click="switchView('todos')">
        <span class="nav-icon">✓</span>
        <span class="nav-text">待办</span>
      </div>
      <div class="nav-item" :class="{ 'active': currentView === 'data' }" @click="switchView('data')">
        <span class="nav-icon">📊</span>
        <span class="nav-text">数据</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  padding: 0 20px;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 6px 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 20px;
  min-width: 55px;
  gap: 1px;
}

.nav-item:hover:not(.active) {
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
}

.nav-item.active {
  color: #fff;
  background-color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

.nav-icon {
  font-size: 14px;
  margin-bottom: 1px;
  transition: transform 0.2s ease;
}

.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1;
}
</style>