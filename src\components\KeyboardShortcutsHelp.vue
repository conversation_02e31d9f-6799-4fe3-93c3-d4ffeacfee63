<template>
  <div v-if="show" class="shortcuts-overlay" @click="$emit('close')">
    <div class="shortcuts-dialog" @click.stop>
      <div class="shortcuts-header">
        <h3 class="shortcuts-title">键盘快捷键</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="shortcuts-content">
        <div class="shortcuts-section">
          <h4 class="section-title">笔记操作</h4>
          <div class="shortcut-item">
            <span class="shortcut-keys">Ctrl + N</span>
            <span class="shortcut-desc">创建新笔记</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-keys">Escape</span>
            <span class="shortcut-desc">关闭编辑器</span>
          </div>
        </div>
        
        <div class="shortcuts-section">
          <h4 class="section-title">文件夹操作</h4>
          <div class="shortcut-item">
            <span class="shortcut-keys">Ctrl + Shift + N</span>
            <span class="shortcut-desc">创建新文件夹</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-keys">Ctrl + B</span>
            <span class="shortcut-desc">切换侧边栏</span>
          </div>
        </div>
        
        <div class="shortcuts-section">
          <h4 class="section-title">搜索和导航</h4>
          <div class="shortcut-item">
            <span class="shortcut-keys">Ctrl + F</span>
            <span class="shortcut-desc">聚焦搜索框</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-keys">Escape</span>
            <span class="shortcut-desc">清空搜索</span>
          </div>
        </div>
        
        <div class="shortcuts-section">
          <h4 class="section-title">帮助</h4>
          <div class="shortcut-item">
            <span class="shortcut-keys">F1 或 ?</span>
            <span class="shortcut-desc">显示快捷键帮助</span>
          </div>
        </div>
      </div>
      
      <div class="shortcuts-footer">
        <p class="shortcuts-tip">💡 提示：大部分操作都有对应的快捷键，熟练使用可以大大提高效率！</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  show: boolean;
}

defineProps<Props>();
defineEmits<{
  'close': [];
}>();
</script>

<style scoped>
.shortcuts-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.shortcuts-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.shortcuts-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.shortcuts-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.shortcuts-section {
  margin-bottom: 24px;
}

.shortcuts-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 2px solid #e5e7eb;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-keys {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  font-weight: 600;
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  min-width: 120px;
  text-align: center;
}

.shortcut-desc {
  flex: 1;
  margin-left: 16px;
  font-size: 14px;
  color: #6b7280;
}

.shortcuts-footer {
  padding: 16px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.shortcuts-tip {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shortcuts-dialog {
    width: 95%;
    max-height: 90vh;
  }
  
  .shortcuts-header {
    padding: 16px 20px;
  }
  
  .shortcuts-title {
    font-size: 18px;
  }
  
  .shortcuts-content {
    padding: 20px;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .shortcut-keys {
    min-width: auto;
    align-self: flex-start;
  }
  
  .shortcut-desc {
    margin-left: 0;
    font-size: 13px;
  }
}

/* 滚动条样式 */
.shortcuts-content::-webkit-scrollbar {
  width: 6px;
}

.shortcuts-content::-webkit-scrollbar-track {
  background: transparent;
}

.shortcuts-content::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.shortcuts-content::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}
</style>