<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import AppHeader from '../components/AppHeader.vue';
import FolderSidebar from '../components/FolderSidebar.vue';
import NoteCard from '../components/NoteCard.vue';
import ContextMenu from '../components/ContextMenu.vue';
import NoteEditor from '../components/NoteEditor.vue';
import AddButton from '../components/AddButton.vue';
import Notification from '../components/Notification.vue';
import ConfirmDialog from '../components/ConfirmDialog.vue';
import CreateFolderDialog from '../components/CreateFolderDialog.vue';
import FolderContextMenu from '../components/FolderContextMenu.vue';
import RenameFolderDialog from '../components/RenameFolderDialog.vue';
import MoveFolderDialog from '../components/MoveFolderDialog.vue';
import FolderBreadcrumb from '../components/FolderBreadcrumb.vue';
import KeyboardShortcutsHelp from '../components/KeyboardShortcutsHelp.vue';
import { FolderService } from '../services/FolderService.js';

// 笔记状态
const notes = ref<string>('');
const title = ref<string>('');
const searchQuery = ref<string>('');
const showEditor = ref<boolean>(false);
const savedNotes = ref<Array<{ id: string, title: string, content: string, date: string, isPinned?: boolean }>>([]);
const editingNoteId = ref<string>('');
// 搜索高亮状态
const highlightedNoteId = ref<string>('');

// 文件夹相关状态
const currentFolderId = ref<string | null>(null);
const showSidebar = ref<boolean>(false);
const folderSidebarRef = ref<InstanceType<typeof FolderSidebar> | null>(null);
const showCreateFolderDialog = ref<boolean>(false);
const createFolderDialogRef = ref<InstanceType<typeof CreateFolderDialog> | null>(null);

// 文件夹管理状态
const showFolderContextMenu = ref<boolean>(false);
const folderContextMenuPosition = ref({ x: 0, y: 0 });
const selectedFolderId = ref<string>('');
const showRenameFolderDialog = ref<boolean>(false);
const renameFolderDialogRef = ref<InstanceType<typeof RenameFolderDialog> | null>(null);
const currentFolderName = ref<string>('');

// 笔记移动状态
const showMoveFolderDialog = ref<boolean>(false);
const moveFolderDialogRef = ref<InstanceType<typeof MoveFolderDialog> | null>(null);
const selectedNoteId = ref<string>('');

// 通知状态
const notification = ref<{ show: boolean, message: string, type: 'success' | 'error' | 'info' }>({
  show: false,
  message: '',
  type: 'info'
});

// 确认对话框状态
const confirmDialog = ref<{
  show: boolean,
  message: string,
  onConfirm: () => void,
  onCancel: () => void
}>({
  show: false,
  message: '',
  onConfirm: () => { },
  onCancel: () => { }
});

// 右键菜单状态
const showContextMenu = ref<boolean>(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const selectedNoteIndex = ref<number>(-1);
const isBatchDeleteMode = ref<boolean>(false);
const selectedNotes = ref<Set<number>>(new Set());

// 显示通知
const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  notification.value = {
    show: true,
    message,
    type
  };

  // 3秒后自动关闭通知
  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};

// 显示确认对话框
const showConfirmDialog = (message: string, onConfirm: () => void, onCancel: () => void = () => { }) => {
  confirmDialog.value = {
    show: true,
    message,
    onConfirm,
    onCancel
  };
};

// 关闭确认对话框
const closeConfirmDialog = () => {
  confirmDialog.value.show = false;
};

// 切换批量删除模式
const toggleBatchDelete = () => {
  isBatchDeleteMode.value = !isBatchDeleteMode.value;
  selectedNotes.value.clear();
};

// 切换笔记选中状态
const toggleNoteSelection = (index: number, event: Event) => {
  if (!isBatchDeleteMode.value) return;
  event.stopPropagation();
  if (selectedNotes.value.has(index)) {
    selectedNotes.value.delete(index);
  } else {
    selectedNotes.value.add(index);
  }
};

// 确认批量删除
const confirmBatchDelete = () => {
  if (selectedNotes.value.size === 0) {
    showNotification('请至少选择一条笔记', 'error');
    return;
  }

  showConfirmDialog(
    `确定要删除选中的 ${selectedNotes.value.size} 条笔记吗？`,
    () => {
      const sortedIndices = Array.from(selectedNotes.value).sort((a, b) => b - a);
      sortedIndices.forEach(index => {
        savedNotes.value.splice(index, 1);
      });
      utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
      isBatchDeleteMode.value = false;
      selectedNotes.value.clear();
      showNotification(`已删除 ${sortedIndices.length} 条笔记`, 'info');
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

// 键盘快捷键处理
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // Ctrl+N: 创建新笔记
  if (event.ctrlKey && event.key === 'n' && !event.shiftKey) {
    event.preventDefault();
    createNewNote();
    return;
  }
  
  // Ctrl+Shift+N: 创建文件夹（由FolderSidebar处理）
  // 这里不需要处理，因为FolderSidebar已经处理了
  
  // Ctrl+B: 切换侧边栏
  if (event.ctrlKey && event.key === 'b') {
    event.preventDefault();
    toggleSidebar();
    return;
  }
  
  // Ctrl+F: 聚焦搜索框
  if (event.ctrlKey && event.key === 'f') {
    event.preventDefault();
    // 聚焦搜索框
    const searchInput = document.querySelector('.search-input') as HTMLInputElement;
    if (searchInput) {
      searchInput.focus();
    }
    return;
  }
  
  // Escape: 关闭编辑器或清空搜索
  if (event.key === 'Escape') {
    if (showKeyboardHelp.value) {
      event.preventDefault();
      showKeyboardHelp.value = false;
    } else if (showEditor.value) {
      event.preventDefault();
      cancelEdit();
    }
    return;
  }
  
  // F1 或 ?: 显示快捷键帮助
  if (event.key === 'F1' || (event.shiftKey && event.key === '?')) {
    event.preventDefault();
    showKeyboardHelp.value = true;
    return;
  }
};

// 从本地存储加载笔记
onMounted(() => {
  const storedNotes = utools.dbStorage.getItem('notes');
  if (storedNotes) {
    savedNotes.value = JSON.parse(storedNotes);
  }
  console.log('savedNotes.value', savedNotes.value);
  
  // 添加全局点击事件监听器，用于关闭右键菜单
  document.addEventListener('click', () => {
    showContextMenu.value = false;
    showFolderContextMenu.value = false;
  });
  
  // 添加键盘快捷键监听器
  document.addEventListener('keydown', handleGlobalKeydown);
  
  // 响应式设计：根据屏幕尺寸自动调整侧边栏显示
  const handleResize = () => {
    const isMobile = window.innerWidth <= 768;
    if (isMobile && showSidebar.value) {
      // 在小屏幕上默认隐藏侧边栏
      showSidebar.value = false;
    } else if (!isMobile && !showSidebar.value) {
      // 在大屏幕上默认显示侧边栏
      showSidebar.value = false;
    }
  };
  
  // 初始检查
  handleResize();
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 清理事件监听器
  return () => {
    window.removeEventListener('resize', handleResize);
    document.removeEventListener('keydown', handleGlobalKeydown);
  };
});

// 排序选项
const sortOption = ref<'date' | 'title' | 'pinned'>('pinned');
const sortOrder = ref<'asc' | 'desc'>('desc');

// 搜索模式状态
const searchMode = ref<'current' | 'global'>('global'); // current: 当前文件夹内搜索, global: 全局搜索
const searchResults = ref<Array<{ note: any, folderInfo: { id: string | null, name: string, path: string[] } }>>([]);

// 快捷键帮助状态
const showKeyboardHelp = ref<boolean>(false);

// 获取笔记的文件夹信息
const getNoteFolder = (note: any) => {
  const folderId = note.folderId;
  if (!folderId) {
    return { id: null, name: '根目录', path: ['根目录'] };
  }
  
  const folders = FolderService.getAllFolders();
  const folder = folders.find(f => f.id === folderId);
  if (!folder) {
    return { id: null, name: '根目录', path: ['根目录'] };
  }
  
  // 构建文件夹路径
  const path = [folder.name];
  let currentFolder = folder;
  while (currentFolder.parentId) {
    const parentFolder = folders.find(f => f.id === currentFolder.parentId);
    if (parentFolder) {
      path.unshift(parentFolder.name);
      currentFolder = parentFolder;
    } else {
      break;
    }
  }
  
  return { id: folderId, name: folder.name, path };
};

// 执行全局搜索
const performGlobalSearch = (query: string) => {
  if (!query.trim()) {
    searchResults.value = [];
    return [];
  }
  
  const lowerQuery = query.toLowerCase().trim();
  const allNotes = [...savedNotes.value];
  
  const results = allNotes
    .filter(note => {
      const titleMatch = note.title.toLowerCase().includes(lowerQuery);
      const contentMatch = note.content.toLowerCase().includes(lowerQuery);
      return titleMatch || contentMatch;
    })
    .map(note => ({
      note,
      folderInfo: getNoteFolder(note)
    }));
  
  searchResults.value = results;
  return results.map(r => r.note);
};

// 过滤并排序笔记
const filteredNotes = computed(() => {
  let notes = [...savedNotes.value];

  // 如果有搜索查询，根据搜索模式进行处理
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase().trim();
    if (query) {
      if (searchMode.value === 'global') {
        // 全局搜索：搜索所有笔记
        notes = performGlobalSearch(query);
      } else {
        // 当前文件夹内搜索：先按文件夹过滤，再搜索
        if (currentFolderId.value !== null) {
          notes = notes.filter(note => (note as any).folderId === currentFolderId.value);
        } else {
          // 在根目录搜索时，只搜索没有文件夹ID的笔记
          notes = notes.filter(note => !(note as any).folderId);
        }

        notes = notes.filter(note => {
          const titleMatch = note.title.toLowerCase().includes(query);
          const contentMatch = note.content.toLowerCase().includes(query);
          return titleMatch || contentMatch;
        });
      }
    }
  } else {
    // 没有搜索查询时，按文件夹过滤
    if (currentFolderId.value !== null) {
      // 显示特定文件夹中的笔记
      notes = notes.filter(note => (note as any).folderId === currentFolderId.value);
    }
    // 当 currentFolderId 为 null 时，显示所有笔记（不进行过滤）
    // 这样"所有笔记"就会显示所有笔记，包括有文件夹ID和没有文件夹ID的
  }

  // 排序逻辑
  return notes.sort((a, b) => {
    // 首先按置顶状态排序（置顶笔记始终在前）
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    // 如果置顶状态相同，按选择的排序方式排序
    let comparison = 0;
    
    switch (sortOption.value) {
      case 'date':
        // 按日期排序
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        comparison = dateA - dateB;
        break;
      case 'title':
        // 按标题排序
        comparison = a.title.localeCompare(b.title, 'zh-CN');
        break;
      case 'pinned':
      default:
        // 默认按创建时间排序（通过ID判断）
        comparison = parseInt(a.id) - parseInt(b.id);
        break;
    }
    
    // 应用排序顺序
    return sortOrder.value === 'asc' ? comparison : -comparison;
  });
});

// 切换排序方式
const changeSortOption = (value: string) => {
  const [option, order] = value.split('-');
  sortOption.value = option as 'date' | 'title' | 'pinned';
  sortOrder.value = order as 'asc' | 'desc';
};

// 获取当前文件夹的笔记统计
const currentFolderStats = computed(() => {
  const total = filteredNotes.value.length;
  const pinned = filteredNotes.value.filter(note => note.isPinned).length;
  return { total, pinned };
});

// 保存笔记
const saveNote = (data: { id: string; title: string; content: string; reminderDate?: string; reminderTime?: string }) => {
  console.log('Notes.vue 接收到的保存数据:', data);

  if (data.title == null || data.content == null || !data.title.trim() || !data.content.trim()) {
    showNotification('标题和内容不能为空！', 'error');
    return;
  }

  if (editingNoteId.value) {
    // 更新现有笔记
    const index = savedNotes.value.findIndex(note => note.id === editingNoteId.value);
    if (index !== -1) {
      savedNotes.value[index] = {
        ...savedNotes.value[index],
        title: data.title,
        content: data.content,
        date: new Date().toLocaleString()
      };
      // utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
      utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
      showNotification('笔记已更新', 'success');
    }
  } else {
    // 创建新笔记
    const newNote = {
      id: Date.now().toString(),
      title: data.title.trim(),
      content: data.content.trim(),
      date: new Date().toLocaleString(),
      isPinned: false,
      reminderDate: data.reminderDate || '',
      reminderTime: data.reminderTime || '',
      folderId: currentFolderId.value // 将笔记分配到当前文件夹
    };
    savedNotes.value.push(newNote);
    utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
    
    // 根据当前文件夹显示不同的成功消息
    const folderName = currentFolderId.value ? 
      FolderService.getAllFolders().find(f => f.id === currentFolderId.value)?.name : 
      '根目录';
    showNotification(`笔记已保存到${folderName}`, 'success');
  }

  showEditor.value = false;
  editingNoteId.value = ''; // 重置编辑中的笔记ID
};

// 加载笔记
const loadNote = (note: { id: string, title: string, content: string, date: string }) => {
  console.log('加载笔记数据:', note);

  // 先设置编辑中的笔记ID
  editingNoteId.value = note.id;
  // 然后设置标题和内容
  title.value = note.title;
  notes.value = note.content;

  console.log('传递给编辑器的数据:', {
    id: editingNoteId.value,
    title: title.value,
    notes: notes.value
  });

  // 最后显示编辑器
  showEditor.value = true;
};

// 删除笔记
const deleteNote = (index: number) => {
  showConfirmDialog(
    '确定要删除这条笔记吗？',
    () => {
      savedNotes.value.splice(index, 1);
      utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
      showContextMenu.value = false;
      showNotification('笔记已删除', 'info');
      closeConfirmDialog();
    },
    () => {
      showContextMenu.value = false;
      closeConfirmDialog();
    }
  );
};

// 置顶/取消置顶笔记
const togglePinNote = (index: number) => {
  const note = savedNotes.value[index];
  note.isPinned = !note.isPinned;
  utools.dbStorage.setItem('notes', JSON.stringify(savedNotes.value));
  showContextMenu.value = false;
  showNotification(note.isPinned ? '笔记已置顶' : '笔记已取消置顶', 'success');
};

// 创建新笔记
const createNewNote = () => {
  title.value = '';
  notes.value = '';
  editingNoteId.value = ''; // 重置编辑中的笔记ID

  console.log('创建新笔记，初始数据:', {
    id: editingNoteId.value,
    title: title.value,
    notes: notes.value
  });

  showEditor.value = true;
};

// 显示右键菜单
const showNoteContextMenu = (event: MouseEvent, index: number) => {
  event.preventDefault();
  selectedNoteIndex.value = index;
  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  };
  showContextMenu.value = true;
};

// 添加到待办
const addToTodo = (index: number) => {
  const note = savedNotes.value[index];
  const todoItem = {
    id: Date.now().toString(), // 添加唯一ID
    title: note.title,
    content: note.content,
    date: new Date().toLocaleString(),
    isPinned: false
  };

  // 获取现有待办
  const storedTodos = utools.dbStorage.getItem('todos');
  const todos = storedTodos ? JSON.parse(storedTodos) : [];

  // 添加新待办
  todos.push(todoItem);
  utools.dbStorage.setItem('todos', JSON.stringify(todos));
  showContextMenu.value = false;
  showNotification('已添加到待办', 'success');
};

// 取消编辑
const cancelEdit = () => {
  title.value = '';
  notes.value = '';
  showEditor.value = false;
};

// 笔记网格容器引用
const notesGridRef = ref<HTMLElement | null>(null);

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query;
  
  // 如果有搜索关键词，等待DOM更新后滚动到第一个匹配的笔记
  if (query) {
    // 重置高亮状态
    highlightedNoteId.value = '';
    
    // 使用 nextTick 确保在 DOM 更新后执行滚动
    nextTick(() => {
      // 确保过滤后的笔记列表已更新
      if (filteredNotes.value.length > 0) {
        // 设置第一个匹配笔记为高亮
        highlightedNoteId.value = filteredNotes.value[0].id;
        
        // 使用 setTimeout 确保 DOM 完全更新
        setTimeout(() => {
          try {
            // 获取笔记网格容器
            const notesGrid = notesGridRef.value || document.querySelector('.notes-grid') as HTMLElement;
            if (!notesGrid) {
              console.error('找不到笔记网格容器');
              return;
            }
            
            // 直接查找匹配 ID 的元素
            const targetId = filteredNotes.value[0].id;
            const selector = `.note-card[data-note-id="${targetId}"]`;
            const targetElement = document.querySelector(selector) as HTMLElement;
            
            if (targetElement) {
              console.log('找到目标元素，准备滚动');
              
              // 计算滚动位置
              notesGrid.scrollTop = targetElement.offsetTop - 20;
              
              console.log('滚动位置:', notesGrid.scrollTop);
            } else {
              console.error('找不到匹配的笔记元素', { selector });
            }
          } catch (error) {
            console.error('滚动过程中出错:', error);
          }
        }, 100);
      }
    });
  } else {
    // 清空搜索时清除高亮和搜索结果
    highlightedNoteId.value = '';
    searchResults.value = [];
  }
};

// 文件夹相关方法
const selectFolder = (folderId: string | null) => {
  currentFolderId.value = folderId;
  // 清空搜索和高亮状态
  searchQuery.value = '';
  highlightedNoteId.value = '';
};

const handleCreateFolder = () => {
  showCreateFolderDialog.value = true;
};

const handleCreateFolderConfirm = async (name: string, parentId: string | null) => {
  try {
    const result = FolderService.createFolder({
      name,
      parentId
    });
    
    if (result.success) {
      showNotification(result.message, 'success');
      showCreateFolderDialog.value = false;
      // 刷新文件夹树
      folderSidebarRef.value?.refreshFolderTree();
      createFolderDialogRef.value?.resetForm();
    } else {
      // 显示错误信息在对话框中
      createFolderDialogRef.value?.showError(result.message);
    }
  } catch (error) {
    console.error('创建文件夹失败:', error);
    createFolderDialogRef.value?.showError('创建文件夹失败，请重试');
  }
};

const handleCreateFolderCancel = () => {
  showCreateFolderDialog.value = false;
};

const handleFolderContextMenu = (event: { folderId: string; position: { x: number; y: number } }) => {
  selectedFolderId.value = event.folderId;
  folderContextMenuPosition.value = event.position;
  showFolderContextMenu.value = true;
  
  // 获取当前文件夹名称
  const folders = FolderService.getAllFolders();
  const folder = folders.find(f => f.id === event.folderId);
  currentFolderName.value = folder?.name || '';
};

// 文件夹重命名
const handleFolderRename = (folderId: string) => {
  showFolderContextMenu.value = false;
  showRenameFolderDialog.value = true;
};

const handleRenameFolderConfirm = (folderId: string, newName: string) => {
  try {
    const result = FolderService.renameFolder(folderId, newName);
    
    if (result.success) {
      showNotification(result.message, 'success');
      showRenameFolderDialog.value = false;
      // 刷新文件夹树
      folderSidebarRef.value?.refreshFolderTree();
      renameFolderDialogRef.value?.resetForm();
    } else {
      // 显示错误信息在对话框中
      renameFolderDialogRef.value?.showError(result.message);
    }
  } catch (error) {
    console.error('重命名文件夹失败:', error);
    renameFolderDialogRef.value?.showError('重命名文件夹失败，请重试');
  }
};

const handleRenameFolderCancel = () => {
  showRenameFolderDialog.value = false;
};

// 文件夹删除
const handleFolderDelete = (folderId: string) => {
  showFolderContextMenu.value = false;
  
  // 获取文件夹信息
  const folders = FolderService.getAllFolders();
  const folder = folders.find(f => f.id === folderId);
  if (!folder) {
    showNotification('文件夹不存在', 'error');
    return;
  }
  
  // 获取文件夹中的笔记数量
  const notes = FolderService.getNotesInFolder(folderId);
  const noteCount = notes.length;
  
  let message = `确定要删除文件夹"${folder.name}"吗？`;
  if (noteCount > 0) {
    message += `\n文件夹中的 ${noteCount} 条笔记将移动到根目录。`;
  }
  
  showConfirmDialog(
    message,
    () => {
      const result = FolderService.deleteFolder(folderId);
      if (result.success) {
        showNotification(result.message, 'success');
        // 如果删除的是当前选中的文件夹，切换到根目录
        if (currentFolderId.value === folderId) {
          currentFolderId.value = null;
        }
        // 刷新文件夹树和笔记列表
        folderSidebarRef.value?.refreshFolderTree();
        // 重新加载笔记数据
        const storedNotes = utools.dbStorage.getItem('notes');
        if (storedNotes) {
          savedNotes.value = JSON.parse(storedNotes);
        }
      } else {
        showNotification(result.message, 'error');
      }
      closeConfirmDialog();
    },
    () => {
      closeConfirmDialog();
    }
  );
};

const handleDropNote = (event: { noteId: string; targetFolderId: string | null; noteTitle?: string }) => {
  // 获取笔记标题用于显示
  const noteTitle = event.noteTitle || savedNotes.value.find(note => note.id === event.noteId)?.title || '未知笔记';
  
  // 获取目标文件夹名称
  let targetFolderName = '根目录';
  if (event.targetFolderId) {
    const folders = FolderService.getAllFolders();
    const targetFolder = folders.find(f => f.id === event.targetFolderId);
    targetFolderName = targetFolder?.name || '未知文件夹';
  }
  
  // 显示移动中的提示
  showNotification(`正在移动笔记"${noteTitle}"到${targetFolderName}...`, 'info');
  
  // 移动笔记到文件夹
  try {
    const result = FolderService.moveNoteToFolder({
      noteId: event.noteId,
      targetFolderId: event.targetFolderId
    });
    
    if (result.success) {
      showNotification(`笔记"${noteTitle}"已成功移动到${targetFolderName}`, 'success');
      // 刷新文件夹树和笔记列表
      folderSidebarRef.value?.refreshFolderTree();
      // 重新加载笔记数据
      const storedNotes = utools.dbStorage.getItem('notes');
      if (storedNotes) {
        savedNotes.value = JSON.parse(storedNotes);
      }
    } else {
      showNotification(`移动失败：${result.message}`, 'error');
    }
  } catch (error) {
    console.error('拖拽移动笔记失败:', error);
    showNotification(`移动笔记"${noteTitle}"失败，请重试`, 'error');
  }
};

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value;
};

// 笔记移动功能
const handleMoveToFolder = (index: number) => {
  showContextMenu.value = false;
  const note = savedNotes.value[index];
  selectedNoteId.value = note.id;
  showMoveFolderDialog.value = true;
};

const handleMoveFolderConfirm = (noteId: string, targetFolderId: string | null) => {
  try {
    const result = FolderService.moveNoteToFolder({
      noteId,
      targetFolderId
    });
    
    if (result.success) {
      showNotification(result.message, 'success');
      showMoveFolderDialog.value = false;
      // 刷新文件夹树和笔记列表
      folderSidebarRef.value?.refreshFolderTree();
      // 重新加载笔记数据
      const storedNotes = utools.dbStorage.getItem('notes');
      if (storedNotes) {
        savedNotes.value = JSON.parse(storedNotes);
      }
      moveFolderDialogRef.value?.resetForm();
    } else {
      showNotification(result.message, 'error');
    }
  } catch (error) {
    console.error('移动笔记失败:', error);
    showNotification('移动笔记失败，请重试', 'error');
  }
};

const handleMoveFolderCancel = () => {
  showMoveFolderDialog.value = false;
};

// 导航到指定文件夹
const navigateToFolder = (folderId: string | null) => {
  // 清空搜索状态
  searchQuery.value = '';
  highlightedNoteId.value = '';
  // 切换到指定文件夹
  selectFolder(folderId);
  showNotification(`已切换到${folderId ? '文件夹' : '根目录'}`, 'info');
};
</script>

<template>
  <div class="notebook">
    <!-- 通知组件 -->
    <Notification :show="notification.show" :message="notification.message" :type="notification.type"
      @close="notification.show = false" />

    <!-- 确认对话框组件 -->
    <ConfirmDialog :show="confirmDialog.show" :message="confirmDialog.message" @confirm="confirmDialog.onConfirm"
      @cancel="confirmDialog.onCancel" />

    <!-- 创建文件夹对话框 -->
    <CreateFolderDialog
      ref="createFolderDialogRef"
      :show="showCreateFolderDialog"
      @confirm="handleCreateFolderConfirm"
      @cancel="handleCreateFolderCancel"
    />

    <!-- 文件夹右键菜单 -->
    <FolderContextMenu
      :show="showFolderContextMenu"
      :position="folderContextMenuPosition"
      :folder-id="selectedFolderId"
      @rename="handleFolderRename"
      @delete="handleFolderDelete"
    />

    <!-- 重命名文件夹对话框 -->
    <RenameFolderDialog
      ref="renameFolderDialogRef"
      :show="showRenameFolderDialog"
      :folder-id="selectedFolderId"
      :current-name="currentFolderName"
      @confirm="handleRenameFolderConfirm"
      @cancel="handleRenameFolderCancel"
    />

    <!-- 移动笔记到文件夹对话框 -->
    <MoveFolderDialog
      ref="moveFolderDialogRef"
      :show="showMoveFolderDialog"
      :note-id="selectedNoteId"
      @confirm="handleMoveFolderConfirm"
      @cancel="handleMoveFolderCancel"
    />

    <!-- 编辑器视图 -->
    <NoteEditor v-if="showEditor" :id="editingNoteId" :initial-title="title" :initial-content="notes"
      placeholder="输入笔记内容..." @save="saveNote" @cancel="cancelEdit" @showConfirm="(data) => {
        showConfirmDialog(
          data.message,
          () => {
            data.onConfirm();
            closeConfirmDialog();
          },
          () => {
            closeConfirmDialog();
          }
        );
      }" />

    <!-- 笔记列表视图 -->
    <div v-else class="notes-view" :class="{ 'with-sidebar': showSidebar }">
      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 文件夹侧边栏 -->
        <FolderSidebar
          v-if="showSidebar"
          ref="folderSidebarRef"
          :current-folder-id="currentFolderId"
          @select-folder="selectFolder"
          @create-folder="handleCreateFolder"
          @folder-context-menu="handleFolderContextMenu"
          @drop-note="handleDropNote"
        />
        
        <!-- 笔记内容区域 -->
        <div class="notes-content" :class="{ 'full-width': !showSidebar }">
          <AppHeader
              title="笔记"
              :is-batch-mode="isBatchDeleteMode"
              :show-sidebar="showSidebar"
              :sort-option="sortOption"
              :sort-order="sortOrder"
              @toggle-batch-mode="toggleBatchDelete"
              @delete-selected="confirmBatchDelete"
              @search="handleSearch"
              @toggle-sidebar="toggleSidebar"
              @sort-change="changeSortOption"
            />

          <!-- 面包屑导航 -->
          <!-- <FolderBreadcrumb
            :current-folder-id="currentFolderId"
            @navigate="selectFolder"
          /> -->

          <!-- 搜索模式切换栏 -->
          <div v-if="searchQuery" class="search-mode-bar">
            <div class="search-info">
              <span class="search-query">搜索: "{{ searchQuery }}"</span>
              <span class="search-results-count">找到 {{ filteredNotes.length }} 条结果</span>
            </div>
            <div class="search-mode-controls">
              <button 
                class="mode-btn" 
                :class="{ active: searchMode === 'current' }"
                @click="searchMode = 'current'"
                title="在当前文件夹内搜索"
              >
                📁 当前文件夹
              </button>
              <button 
                class="mode-btn" 
                :class="{ active: searchMode === 'global' }"
                @click="searchMode = 'global'"
                title="全局搜索所有笔记"
              >
                🌐 全局搜索
              </button>
            </div>
          </div>

          

          <!-- 笔记网格或空状态显示 -->
          <div v-if="filteredNotes.length > 0" class="notes-grid" ref="notesGridRef">
            <div v-for="(note, index) in filteredNotes" :key="index" class="note-wrapper">
              <!-- 全局搜索时显示文件夹信息 - 已隐藏 -->
              <!-- <div v-if="searchQuery && searchMode === 'global'" class="note-folder-info">
                <span class="folder-icon">📁</span>
                <span class="folder-path" @click="navigateToFolder(getNoteFolder(note).id)">
                  {{ getNoteFolder(note).path.join(' / ') }}
                </span>
              </div> -->
              
              <NoteCard
                :title="note.title"
                :content="note.content"
                :date="note.date"
                :folder-info="getNoteFolder(note)"
                :is-pinned="note.isPinned"
                :is-selected="isBatchDeleteMode && selectedNotes.has(savedNotes.indexOf(note))"
                :is-batch-mode="isBatchDeleteMode"
                :layout-mode="'note'"
                :class="{ 'highlight-search': note.id === highlightedNoteId }"
                :dataNoteId="note.id"
                @click="isBatchDeleteMode ? toggleNoteSelection(savedNotes.indexOf(note), $event) : loadNote(note)"
                @context-menu="!isBatchDeleteMode && showNoteContextMenu($event, savedNotes.indexOf(note))"
                @toggle-selection="toggleNoteSelection(savedNotes.indexOf(note), $event)"
              />
            </div>
          </div>
          
          <!-- 空状态显示 -->
          <div v-else class="empty-state">
            <div class="empty-icon">📝</div>
            <h3 class="empty-title">
              {{ searchQuery ? '没有找到匹配的笔记' : (currentFolderId ? '文件夹为空' : '还没有笔记') }}
            </h3>
            <p class="empty-description">
              {{ searchQuery ? '尝试使用不同的关键词搜索' : (currentFolderId ? '点击下方按钮在此文件夹中创建第一条笔记' : '点击下方按钮创建你的第一条笔记') }}
            </p>
            <button v-if="!searchQuery" class="empty-action-btn" @click="createNewNote">
              <span class="btn-icon">+</span>
              创建笔记
            </button>
          </div>

          <AddButton @click="createNewNote" />
        </div>
      </div>

      <!-- 右键菜单 -->
      <ContextMenu :show="showContextMenu" :position="contextMenuPosition"
        :is-pinned="selectedNoteIndex >= 0 ? savedNotes[selectedNoteIndex]?.isPinned : false" type="note"
        @toggle-pin="togglePinNote(selectedNoteIndex)" @delete="deleteNote(selectedNoteIndex)"
        @add-to-todo="addToTodo(selectedNoteIndex)" @move-to-folder="handleMoveToFolder(selectedNoteIndex)" />

      <!-- 快捷键帮助 -->
      <KeyboardShortcutsHelp 
        :show="showKeyboardHelp" 
        @close="showKeyboardHelp = false" 
      />
    </div>
  </div>
</template>

<style scoped>
.notebook {
  max-width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #f8f9fa;
}

.notes-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-bottom: 80px;
  position: relative;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 当侧边栏显示时，移除左右padding */
.notes-view.with-sidebar {
  padding-left: 0;
  padding-right: 0;
}

.main-content {
  display: flex;
  height: 100%;
  gap: 0;
}

.notes-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notes-content.full-width {
  width: 100%;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  margin-top: 8px;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 20px;
  max-height: calc(100vh - 140px);
  scroll-behavior: smooth;
  width: 100%;
  box-sizing: border-box;
}

/* 搜索模式切换栏样式 */
.search-mode-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #e1bee7;
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.1);
}

.search-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.search-query {
  font-size: 14px;
  font-weight: 600;
  color: #4a148c;
}

.search-results-count {
  font-size: 12px;
  color: #7b1fa2;
  font-weight: 500;
}

.search-mode-controls {
  display: flex;
  gap: 8px;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #ffffff;
  border: 1px solid #ce93d8;
  border-radius: 20px;
  font-size: 13px;
  color: #7b1fa2;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  white-space: nowrap;
}

.mode-btn:hover {
  background: #f8bbd9;
  border-color: #ad52c7;
  color: #4a148c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.2);
}

.mode-btn.active {
  background: #9c27b0;
  border-color: #9c27b0;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.mode-btn.active:hover {
  background: #7b1fa2;
  border-color: #7b1fa2;
}

/* 笔记文件夹信息样式 */
.note-wrapper {
  position: relative;
}

.note-folder-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f0f4ff;
  border: 1px solid #e3f2fd;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  color: #1976d2;
  margin-bottom: -1px;
  z-index: 1;
}

.folder-icon {
  font-size: 12px;
}

.folder-path {
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-path:hover {
  color: #1565c0;
  text-decoration: underline;
}

.note-wrapper .note-folder-info + :deep(.note-card) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top: none;
}



/* 搜索高亮样式 */
:deep(.note-card.highlight-search) {
  box-shadow: 0 0 0 3px #1976d2, 0 4px 12px rgba(25, 118, 210, 0.4);
  animation: pulse 1.5s infinite;
  transform: translateY(-2px);
  z-index: 10;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.8), 0 4px 12px rgba(25, 118, 210, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.6), 0 6px 16px rgba(25, 118, 210, 0.3);
  }
  100% {
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.8), 0 4px 12px rgba(25, 118, 210, 0.4);
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
  margin: 20px 0;
  min-height: 300px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.5;
  max-width: 400px;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.empty-action-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.empty-action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.btn-icon {
  font-size: 18px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    gap: 0;
  }
}

@media (max-width: 768px) {
  .notes-view {
    padding: 8px;
    padding-bottom: 80px;
  }
  
  .main-content {
    flex-direction: column;
    height: auto;
  }
  
  .folder-sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 16px;
  }
  
  .notes-content {
    width: 100% !important;
  }
  
  .notes-content.full-width {
    width: 100%;
  }
  
  .search-mode-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 8px 12px;
  }
  
  .search-mode-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .mode-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    padding: 6px 12px;
    font-size: 12px;
  }
  
  
  
  .sort-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .sort-btn {
    flex: 1;
    min-width: 80px;
    justify-content: center;
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .notes-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    max-height: calc(100vh - 200px);
  }
  
  .note-folder-info {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .folder-path {
    font-size: 11px;
  }
  
  .empty-state {
    padding: 40px 16px;
    min-height: 250px;
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-title {
    font-size: 20px;
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    margin-bottom: 24px;
  }
  
  .empty-action-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .notes-view {
    padding: 4px;
    padding-bottom: 80px;
  }
  
  .search-mode-bar {
    padding: 6px 8px;
  }
  
  
  
  .notes-grid {
    gap: 6px;
    padding-bottom: 16px;
  }
  
  .mode-btn {
    min-width: 100px;
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .sort-btn {
    min-width: 70px;
    padding: 3px 6px;
    font-size: 11px;
  }
  
  .empty-state {
    padding: 30px 12px;
    min-height: 200px;
  }
  
  .empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .empty-title {
    font-size: 18px;
    margin-bottom: 6px;
  }
  
  .empty-description {
    font-size: 13px;
    margin-bottom: 20px;
  }
  
  .empty-action-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}

/* 侧边栏隐藏时的过渡动画 */
.folder-sidebar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 确保在小屏幕上侧边栏完全隐藏时不占用空间 */
@media (max-width: 768px) {
  .folder-sidebar {
    transform: translateX(-100%);
    position: absolute;
    z-index: 1000;
    background: white;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }
  
  .folder-sidebar:not(.hidden) {
    transform: translateX(0);
  }
}
</style>






