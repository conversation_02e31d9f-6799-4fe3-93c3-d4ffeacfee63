<template>
  <div 
    v-if="show" 
    class="context-menu"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @click.stop
  >
    <div class="menu-item" @click="handleRename">
      <span class="menu-icon">✏️</span>
      <span class="menu-text">重命名</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item danger" @click="handleDelete">
      <span class="menu-icon">🗑️</span>
      <span class="menu-text">删除文件夹</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
// Props
interface Props {
  show: boolean;
  position: { x: number; y: number };
  folderId: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'rename': [folderId: string];
  'delete': [folderId: string];
}>();

// 方法
const handleRename = () => {
  emit('rename', props.folderId);
};

const handleDelete = () => {
  emit('delete', props.folderId);
};
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  padding: 4px 0;
  min-width: 140px;
  z-index: 1000;
  animation: menuSlideIn 0.15s ease-out;
}

@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.danger:hover {
  background-color: #ffebee;
  color: #f44336;
}

.menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.menu-text {
  flex: 1;
}

.menu-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 4px 0;
}
</style>