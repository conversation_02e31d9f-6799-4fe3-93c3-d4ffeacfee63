<script lang="ts" setup>
import { ref } from 'vue';
import DataManager from '../utils/DataManager.js';

// 状态
const showDialog = ref(false);
const importOptions = ref({
  mergeNotes: false,
  mergeTodos: false
});
const notificationMessage = ref('');
const showNotification = ref(false);
const notificationType = ref<'success' | 'error' | 'info'>('info');

// 导出所有数据
const exportAllData = () => {
  const data = DataManager.exportAllData();
  const filename = `notes-todos-export-${new Date().toISOString().slice(0, 10)}.json`;
  DataManager.saveToFile(data, filename);
  showNotification.value = true;
  notificationMessage.value = '数据导出成功';
  notificationType.value = 'success';
  setTimeout(() => {
    showNotification.value = false;
  }, 3000);
};

// 仅导出笔记
const exportNotes = () => {
  const data = DataManager.exportNotes();
  const filename = `notes-export-${new Date().toISOString().slice(0, 10)}.json`;
  DataManager.saveToFile(data, filename);
  showNotification.value = true;
  notificationMessage.value = '笔记数据导出成功';
  notificationType.value = 'success';
  setTimeout(() => {
    showNotification.value = false;
  }, 3000);
};

// 仅导出待办
const exportTodos = () => {
  const data = DataManager.exportTodos();
  const filename = `todos-export-${new Date().toISOString().slice(0, 10)}.json`;
  DataManager.saveToFile(data, filename);
  showNotification.value = true;
  notificationMessage.value = '待办数据导出成功';
  notificationType.value = 'success';
  setTimeout(() => {
    showNotification.value = false;
  }, 3000);
};

// 导入数据
const importData = (event: Event) => {
  const fileInput = event.target as HTMLInputElement;
  if (!fileInput.files || fileInput.files.length === 0) {
    return;
  }
  
  const file = fileInput.files[0];
  const reader = new FileReader();
  
  reader.onload = (e) => {
    if (!e.target || typeof e.target.result !== 'string') {
      showNotification.value = true;
      notificationMessage.value = '读取文件失败';
      notificationType.value = 'error';
      return;
    }
    
    const jsonData = e.target.result;
    const result = DataManager.importData(jsonData, importOptions.value);
    
    showNotification.value = true;
    notificationMessage.value = result.message;
    notificationType.value = result.success ? 'success' : 'error';
    
    // 如果导入成功，重置表单
    if (result.success) {
      closeDialog();
      // 重置文件输入框
      fileInput.value = '';
      // 刷新页面以显示新数据（可能需要更优雅的处理方式）
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    }
    
    setTimeout(() => {
      showNotification.value = false;
    }, 3000);
  };
  
  reader.onerror = () => {
    showNotification.value = true;
    notificationMessage.value = '读取文件失败';
    notificationType.value = 'error';
    setTimeout(() => {
      showNotification.value = false;
    }, 3000);
  };
  
  reader.readAsText(file);
};

// 打开导入对话框
const openImportDialog = () => {
  importOptions.value = {
    mergeNotes: false,
    mergeTodos: false
  };
  showDialog.value = true;
};

// 关闭对话框
const closeDialog = () => {
  showDialog.value = false;
};

// 清除所有数据
const clearAllData = () => {
  if (confirm('确定要清除所有数据吗？此操作不可撤销！')) {
    const result = DataManager.clearAllData();
    showNotification.value = true;
    notificationMessage.value = result.message;
    notificationType.value = result.success ? 'info' : 'error';
    setTimeout(() => {
      showNotification.value = false;
      if (result.success) {
        // 刷新页面以反映更改
        window.location.reload();
      }
    }, 1500);
  }
};
</script>

<template>
  <div class="data-management-container">
    <!-- 功能卡片 -->
    <div class="card-grid">
      <!-- 导出功能卡片 -->
      <div class="card export-card">
        <div class="card-header">
          <div class="card-icon">📤</div>
          <div class="card-title-section">
            <h3>导出数据</h3>
            <p>将您的数据导出为JSON文件</p>
          </div>
        </div>
        <div class="card-actions">
          <button @click="exportAllData" class="primary-button">导出所有数据</button>
          <button @click="exportNotes" class="button">仅导出笔记</button>
          <button @click="exportTodos" class="button">仅导出待办</button>
        </div>
      </div>
      
      <!-- 导入功能卡片 -->
      <div class="card import-card">
        <div class="card-header">
          <div class="card-icon">📥</div>
          <div class="card-title-section">
            <h3>导入数据</h3>
            <p>从JSON文件导入数据</p>
          </div>
        </div>
        <div class="card-actions">
          <div class="import-options">
            <label>
              <input type="checkbox" v-model="importOptions.mergeNotes" />
              合并笔记（不覆盖现有数据）
            </label>
            <label>
              <input type="checkbox" v-model="importOptions.mergeTodos" />
              合并待办（不覆盖现有数据）
            </label>
          </div>
          
          <div class="file-input-container">
            <label for="file-input" class="file-input-label">选择文件</label>
            <input id="file-input" type="file" accept=".json" @change="importData" />
          </div>
          <span class="file-name-note">仅支持 .json 格式</span>
        </div>
      </div>
      
      <!-- 危险操作卡片 -->
      <div class="card danger-card">
        <div class="card-header">
          <div class="card-icon">⚠️</div>
          <div class="card-title-section">
            <h3>危险操作</h3>
            <p>谨慎使用以下功能</p>
          </div>
        </div>
        <div class="card-actions">
          <button @click="clearAllData" class="danger-button">
            清除所有数据
          </button>
          <p class="danger-note">此操作将删除所有笔记和待办数据，且不可恢复。</p>
        </div>
      </div>
    </div>
    
    <!-- 通知提示 -->
    <div class="notification" v-if="showNotification" :class="notificationType">
      {{ notificationMessage }}
    </div>
  </div>
</template>

<style scoped>
.data-management-container {
  padding: 0;
  max-width: 860px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  max-width: 100%;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  min-height: 200px;
  max-height: 240px;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.export-card {
  border-left: 4px solid #1976d2;
}

.import-card {
  border-left: 4px solid #4caf50;
}

.card-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.card-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.card-title-section {
  flex: 1;
}

.card-header h3 {
  font-size: 18px;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-weight: 600;
}

.card-header p {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.button, .primary-button, .danger-button {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  height: 32px;
  min-width: 100px;
}

.button {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #e9ecef;
}

.button:hover {
  background-color: #e9ecef;
  color: #212529;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.primary-button {
  background-color: #1976d2;
  color: white;
  border: 1px solid #1976d2;
}

.primary-button:hover {
  background-color: #1565c0;
  border-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(25, 118, 210, 0.3);
}

.danger-button {
  background-color: #f44336;
  color: white;
  border: 1px solid #f44336;
}

.danger-button:hover {
  background-color: #d32f2f;
  border-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(244, 67, 54, 0.3);
}

.danger-card {
  background-color: #fff;
  border-left: 4px solid #f44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.import-options {
  margin-bottom: 8px;
}

.import-options label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.import-options label input {
  margin-right: 5px;
}

.file-input-container {
  margin: 6px 0;
}

.file-input-label {
  padding: 6px 10px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  display: inline-block;
  color: #606266;
  height: 30px;
  line-height: 18px;
}

.file-input-label:hover {
  background-color: #e9ecf2;
  color: #303133;
}

input[type="file"] {
  display: none;
}

.file-name-note {
  font-size: 11px;
  color: #909399;
  margin-top: 3px;
  display: block;
}

.danger-note {
  margin-top: 6px;
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.3;
}

.notification {
  position: fixed;
  top: 16px;
  right: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  z-index: 2000;
  animation: slide-in 0.3s ease-out;
  font-size: 13px;
  max-width: 280px;
}

.success {
  background-color: #67c23a;
  color: white;
}

.error {
  background-color: #f56c6c;
  color: white;
}

.info {
  background-color: #409eff;
  color: white;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 900px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

@media (max-width: 600px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .data-management-container {
    padding: 0;
  }
}
</style> 