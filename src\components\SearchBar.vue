<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索'
  },
  initialValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['search']);

const searchQuery = ref(props.initialValue);

// 执行搜索
const performSearch = () => {
  emit('search', searchQuery.value);
};

// 处理鼠标离开搜索框
const handleMouseLeave = () => {
  performSearch();
};

// 处理回车键
const handleEnter = () => {
  performSearch();
};
</script>

<template>
  <div class="search-bar" @mouseleave="handleMouseLeave">
    <span class="search-icon">🔍</span>
    <input
      v-model="searchQuery"
      type="text"
      :placeholder="placeholder"
      class="search-input"
      @keyup.enter="handleEnter"
    />
    <button class="search-btn" @click="performSearch" title="搜索">
      查找
    </button>
  </div>
</template>

<style scoped>
.search-bar {
  display: flex;
  align-items: center;
  background-color: #f0f4ff;
  border-radius: 20px;
  padding: 8px 16px;
  margin-bottom: 16px;
  border: none;
  transition: all 0.2s ease;
}

.search-bar:focus-within {
  background-color: #e8f0ff;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
}

.search-icon {
  margin-right: 8px;
  color: #1976d2;
  font-size: 14px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
  color: #333;
}

.search-input::placeholder {
  color: #999;
  font-size: 14px;
}

.search-btn {
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 4px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  margin-left: 8px;
}

.search-btn:hover {
  background: #1565c0;
  transform: translateY(-1px);
}

.search-btn:active {
  transform: translateY(0);
}
</style>