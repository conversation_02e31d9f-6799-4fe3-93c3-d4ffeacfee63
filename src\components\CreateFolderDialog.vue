<template>
  <div v-if="show" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">创建文件夹</h3>
        <button class="close-btn" @click="cancel">×</button>
      </div>
      
      <div class="dialog-body">
        <div class="form-group">
          <label class="form-label">文件夹名称</label>
          <input
            ref="nameInput"
            v-model="folderName"
            type="text"
            class="form-input"
            :class="{ error: hasError }"
            placeholder="请输入文件夹名称"
            maxlength="50"
            @keyup.enter="confirm"
            @input="clearError"
          />
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="cancel">取消</button>
        <button 
          class="btn btn-primary" 
          :disabled="!folderName.trim() || isCreating"
          @click="confirm"
        >
          {{ isCreating ? '创建中...' : '确定' }}
        </button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';

interface Props {
  show: boolean;
  parentFolderId?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  parentFolderId: null
});

const emit = defineEmits<{
  'confirm': [name: string, parentId: string | null];
  'cancel': [];
}>();

const folderName = ref('');
const errorMessage = ref('');
const hasError = ref(false);
const isCreating = ref(false);
const nameInput = ref<HTMLInputElement | null>(null);

const confirm = () => {
  const name = folderName.value.trim();
  
  if (!name) {
    showError('文件夹名称不能为空');
    return;
  }
  
  if (name.length > 50) {
    showError('文件夹名称不能超过50个字符');
    return;
  }
  
  isCreating.value = true;
  emit('confirm', name, props.parentFolderId);
};

const cancel = () => {
  emit('cancel');
  resetForm();
};

const handleOverlayClick = () => {
  cancel();
};

const showError = (message: string) => {
  errorMessage.value = message;
  hasError.value = true;
};

const clearError = () => {
  errorMessage.value = '';
  hasError.value = false;
};

const resetForm = () => {
  folderName.value = '';
  errorMessage.value = '';
  hasError.value = false;
  isCreating.value = false;
};

watch(() => props.show, (newShow) => {
  if (newShow) {
    resetForm();
    nextTick(() => {
      nameInput.value?.focus();
    });
  }
});

defineExpose({
  showError,
  resetForm
});
</script>
<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 400px;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 6px;
  font-size: 20px;
  color: #666;
}

.close-btn:hover {
  background-color: #f5f5f5;
}

.dialog-body {
  padding: 20px 24px;
}

.form-group {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1976d2;
}

.form-input.error {
  border-color: #f44336;
}

.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 6px;
}

.dialog-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  min-width: 80px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e9ecef;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1565c0;
}
</style>