<script lang="ts" setup>
defineProps({
  show: {
    type: Boolean,
    required: true
  },
  message: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['confirm', 'cancel']);

// 确认操作
const confirmAction = () => {
  emit('confirm');
};

// 取消操作
const cancelAction = () => {
  emit('cancel');
};
</script>

<template>
  <div v-if="show" class="confirm-dialog-overlay">
    <div class="confirm-dialog">
      <div class="confirm-dialog-content">
        <p class="confirm-dialog-message">{{ message }}</p>
        <div class="confirm-dialog-actions">
          <button class="confirm-dialog-cancel" @click="cancelAction">取消</button>
          <button class="confirm-dialog-confirm" @click="confirmAction">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
  animation: fadeIn 0.25s ease-out;
}

.confirm-dialog {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  width: 75%;
  max-width: 320px;
  min-width: 280px;
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.8);
  overflow: hidden;
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.confirm-dialog::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1976d2, #42a5f5, #1976d2);
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}

.confirm-dialog-content {
  padding: 24px 20px 20px;
  text-align: center;
}

.confirm-dialog-message {
  margin: 0 0 24px 0;
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.5;
  letter-spacing: 0.2px;
}

.confirm-dialog-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 4px;
}

.confirm-dialog-cancel, .confirm-dialog-confirm {
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 50px;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.1px;
}

.confirm-dialog-cancel {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.confirm-dialog-cancel:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  color: #495057;
  border-color: #dee2e6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.confirm-dialog-cancel:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.confirm-dialog-confirm {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  position: relative;
}

.confirm-dialog-confirm::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.confirm-dialog-confirm:hover {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.confirm-dialog-confirm:hover::before {
  left: 100%;
}

.confirm-dialog-confirm:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.35);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .confirm-dialog {
    width: 85%;
    margin: 0 10px;
  }

  .confirm-dialog-content {
    padding: 20px 16px 16px;
  }

  .confirm-dialog-message {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .confirm-dialog-actions {
    flex-direction: column;
    gap: 10px;
  }

  .confirm-dialog-cancel, .confirm-dialog-confirm {
    width: 100%;
    padding: 12px 16px;
  }
}
</style> 