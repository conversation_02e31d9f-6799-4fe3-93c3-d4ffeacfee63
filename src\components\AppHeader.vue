<script lang="ts" setup>
import SearchBar from './SearchBar.vue';

defineProps({
  title: {
    type: String,
    required: true
  },
  isBatchMode: {
    type: Boolean,
    default: false
  },
  isCompactMode: {
    type: Boolean,
    default: true
  },
  showSidebar: {
    type: Boolean,
    default: true
  },
  sortOption: {
    type: String,
    default: 'pinned'
  },
  sortOrder: {
    type: String,
    default: 'desc'
  }
});

const emit = defineEmits(['toggle-batch-mode', 'delete-selected', 'toggle-compact-mode', 'search', 'toggle-sidebar', 'sort-change']);

// 切换批量删除模式
const toggleBatchMode = () => {
  emit('toggle-batch-mode');
};

// 删除选中项
const deleteSelected = () => {
  emit('delete-selected');
};

// 切换紧凑视图模式
const toggleCompactMode = () => {
  emit('toggle-compact-mode');
};

// 处理搜索
const handleSearch = (query: string) => {
  emit('search', query);
};

// 切换侧边栏
const toggleSidebar = () => {
  emit('toggle-sidebar');
};

// 处理排序变更
const handleSortChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  emit('sort-change', target.value);
};
</script>

<template>
  <div class="app-header">
    <div class="app-title">
      <!-- 侧边栏切换按钮 - 仅在笔记页面显示 -->
      <button 
        v-if="title === '笔记'" 
        class="sidebar-toggle-btn" 
        @click="toggleSidebar"
        :class="{ 'active': showSidebar }"
        title="切换侧边栏"
      >
        <span class="toggle-icon">☰</span>
      </button>
      
      <div class="app-icon">📝</div>
      <h1>智能记事本</h1>
    </div>
    <div class="header-actions">
      <!-- 笔记页面的排序下拉选择框 -->
      <div v-if="title === '笔记'" class="sort-container">
        <select 
          class="sort-select" 
          :value="sortOption + '-' + sortOrder"
          @change="handleSortChange"
          title="选择排序方式"
        >
          <option value="pinned-desc">创建时间 ↓</option>
          <option value="pinned-asc">创建时间 ↑</option>
          <option value="date-desc">修改时间 ↓</option>
          <option value="date-asc">修改时间 ↑</option>
          <option value="title-asc">标题 A-Z</option>
          <option value="title-desc">标题 Z-A</option>
        </select>
      </div>

      <!-- 搜索框 - 笔记和待办页面都显示 -->
      <div v-if="title === '笔记' || title === '待办'" class="search-container">
        <SearchBar
          :placeholder="title === '笔记' ? '搜索笔记' : '搜索待办'"
          @search="handleSearch"
        />
      </div>

      <!-- 待办页面的批量删除按钮 -->
      <button v-if="title === '待办'" class="icon-btn delete-btn" @click="toggleBatchMode" :class="{ 'active': isBatchMode }" title="批量删除">
        <span class="material-icon">🗑️</span>
      </button>

      <!-- 批量删除确认按钮 -->
      <button v-if="isBatchMode" class="batch-delete-btn" @click="deleteSelected">
        删除选中项
      </button>
    </div>
  </div>
</template>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  box-shadow: 0 2px 6px rgba(25, 118, 210, 0.3);
}

.app-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  height: 32px;
}



.search-container {
  min-width: 200px;
}

.search-container :deep(.search-bar) {
  margin-bottom: 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 6px 12px;
  height: 32px;
  box-sizing: border-box;
}

.search-container :deep(.search-bar:focus-within) {
  background-color: #e8f0ff;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.search-container :deep(.search-input) {
  font-size: 13px;
}

.search-container :deep(.search-icon) {
  font-size: 12px;
  margin-right: 6px;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #666;
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.delete-btn.active {
  color: #f44336;
  background-color: #ffebee;
}

.batch-delete-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-delete-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

/* 侧边栏切换按钮样式 */
.sidebar-toggle-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: #f8f9fa;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.sidebar-toggle-btn:hover {
  background-color: #e3f2fd;
  color: #1976d2;
  transform: scale(1.05);
}

.sidebar-toggle-btn.active {
  background-color: #1976d2;
  color: white;
  box-shadow: 0 2px 6px rgba(25, 118, 210, 0.3);
}

.sidebar-toggle-btn.active:hover {
  background-color: #1565c0;
}

.toggle-icon {
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
}

/* 排序下拉选择框样式 */
.sort-container {
  margin-right: 8px;
}

.sort-select {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 13px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 32px;
  min-width: 120px;
  box-sizing: border-box;
  outline: none;
}

.sort-select:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.sort-select:focus {
  background-color: #e8f0ff;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.sort-select option {
  padding: 8px 12px;
  font-size: 13px;
}
</style>